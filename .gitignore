# Hugo output
public/

# System files
.DS_Store
Thumbs.db

# Editor settings
.vscode/
.idea/

# Temporary files
*.tmp
*.log

# Node.js dependencies
node_modules/

# Augment Code files
.augment/

# Obsidian configuration
content/.obsidian/
# Now I wanna use GitHub to make a copy of my Ob configs
# Do a test to see if it'll delete the existing files on GitHub. 

# Ob plugin indexing temp files
content/.smtcmp_json_db
content/.smtcmp_vector_db.tar.gz
