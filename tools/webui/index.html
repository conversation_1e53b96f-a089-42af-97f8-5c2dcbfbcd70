<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Hugo 文章编辑器 - 支持 QQ 表情</title>
        <link rel="stylesheet" href="css/style.css" />
        <!-- 引入 daisyUI 和 Tailwind CSS -->
        <link
            href="https://cdn.jsdelivr.net/npm/daisyui@5"
            rel="stylesheet"
            type="text/css"
        />
        <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <!-- 引入 Markdown 编辑器 - Vditor -->
        <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.css"
        />
        <script src="https://cdn.jsdelivr.net/npm/vditor@3.9.6/dist/index.min.js"></script>
    </head>
    <body>
        <div class="container mx-auto max-w-6xl p-4">
            <header class="bg-base-100 rounded-box shadow-lg p-6 mb-6">
                <h1 class="text-2xl font-bold text-primary mb-4">
                    Hugo 文章编辑器
                </h1>
                <div class="toolbar flex flex-wrap gap-2">
                    <button
                        id="newFileBtn"
                        class="btn btn-outline btn-success btn-sm"
                    >
                        新建文章
                    </button>
                    <button
                        id="openFileBtn"
                        class="btn btn-outline btn-primary btn-sm"
                    >
                        打开文章
                    </button>
                    <button id="saveFileBtn" class="btn btn-primary btn-sm">
                        保存文章
                    </button>
                    <div class="divider divider-horizontal"></div>
                    <button
                        id="toggleFrontMatterBtn"
                        class="btn btn-ghost btn-sm"
                    >
                        显示/隐藏 Front Matter
                    </button>
                </div>
            </header>

            <main>
                <div class="editor-container">
                    <div id="frontMatterEditor" class="front-matter-editor">
                        <h3>Front Matter</h3>
                        <div
                            class="front-matter-fields grid grid-cols-1 md:grid-cols-2 gap-4"
                        >
                            <div class="field form-control">
                                <label for="title" class="label">标题</label>
                                <input
                                    type="text"
                                    id="title"
                                    placeholder="文章标题"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control">
                                <label for="date" class="label">日期</label>
                                <input
                                    type="datetime-local"
                                    id="date"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control">
                                <label for="draft" class="label">草稿</label>
                                <select
                                    id="draft"
                                    class="select select-bordered"
                                >
                                    <option value="false">否</option>
                                    <option value="true">是</option>
                                </select>
                            </div>
                            <div class="field form-control">
                                <label for="author" class="label">作者</label>
                                <input
                                    type="text"
                                    id="author"
                                    placeholder="作者"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control">
                                <label for="tags" class="label">标签</label>
                                <input
                                    type="text"
                                    id="tags"
                                    placeholder="标签1, 标签2"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control">
                                <label for="categories" class="label"
                                    >分类</label
                                >
                                <input
                                    type="text"
                                    id="categories"
                                    placeholder="分类1, 分类2"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control">
                                <label for="keywords" class="label"
                                    >关键词</label
                                >
                                <input
                                    type="text"
                                    id="keywords"
                                    placeholder="关键词1, 关键词2"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control">
                                <label for="weight" class="label">权重</label>
                                <input
                                    type="number"
                                    id="weight"
                                    value="0"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="statusCate" class="label"
                                    >状态分类</label
                                >
                                <input
                                    type="text"
                                    id="statusCate"
                                    placeholder="状态分类"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="categoryLink" class="label"
                                    >分类链接</label
                                >
                                <input
                                    type="text"
                                    id="categoryLink"
                                    placeholder="分类链接"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="buyLink" class="label"
                                    >购买链接</label
                                >
                                <input
                                    type="text"
                                    id="buyLink"
                                    placeholder="购买链接"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="buyName" class="label"
                                    >购买名称</label
                                >
                                <input
                                    type="text"
                                    id="buyName"
                                    placeholder="购买名称"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="buyInfo" class="label"
                                    >购买信息</label
                                >
                                <input
                                    type="text"
                                    id="buyInfo"
                                    placeholder="购买信息"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="buyImage" class="label"
                                    >购买图片</label
                                >
                                <input
                                    type="text"
                                    id="buyImage"
                                    placeholder="购买图片URL"
                                    class="input input-bordered"
                                />
                            </div>
                            <div class="field form-control md:col-span-2">
                                <label for="buyButtonText" class="label"
                                    >购买按钮文本</label
                                >
                                <input
                                    type="text"
                                    id="buyButtonText"
                                    placeholder="购买按钮文本"
                                    class="input input-bordered"
                                />
                            </div>
                        </div>
                    </div>

                    <div id="vditor" class="markdown-editor"></div>

                    <div
                        id="qmojiPicker"
                        class="qmoji-picker hidden modal modal-bottom sm:modal-middle"
                    >
                        <div class="modal-box max-w-4xl">
                            <div
                                class="qmoji-picker-header modal-header flex justify-between items-center mb-4"
                            >
                                <h3 class="text-xl font-bold">QQ 表情选择器</h3>
                                <button
                                    id="closeQmojiBtn"
                                    class="btn btn-sm btn-circle btn-ghost"
                                    onclick="toggleQmojiPicker()"
                                >
                                    ✕
                                </button>
                            </div>
                            <div class="qmoji-picker-content">
                                <!-- 表情将通过 JavaScript 动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <footer>
                <p>
                    © 2025 Hugo 文章编辑器 |
                    <a href="https://github.com/Spixed/Qmoji" target="_blank"
                        >Qmoji 项目</a
                    >
                </p>
            </footer>
        </div>

        <script src="js/qmoji-data.js"></script>
        <script src="js/main.js"></script>
    </body>
</html>
