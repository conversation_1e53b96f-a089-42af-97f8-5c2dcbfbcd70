/**
 * Hugo 文章编辑器 - 主要功能
 */

// 全局变量
let vditor = null; // Vditor 编辑器实例
let frontMatterVisible = true; // Front Matter 是否可见

// DOM 元素
const elements = {
    frontMatterEditor: null,
    qmojiPicker: null,
    qmojiPickerContent: null,
    // 按钮
    openFileBtn: null,
    saveFileBtn: null,
    saveBtn: null,
    saveToFileBtn: null,
    // exportBtn 已移除
    importBtn: null,
    toggleFrontMatterBtn: null,
    toggleQmojiBtn: null,
    closeQmojiBtn: null,
    // Front Matter 字段
    titleInput: null,
    dateInput: null,
    draftSelect: null,
    authorInput: null,
    tagsInput: null,
    categoriesInput: null,
    keywordsInput: null,
    weightInput: null,
    statusCateInput: null,
    categoryLinkInput: null,
    buyLinkInput: null,
    buyNameInput: null,
    buyInfoInput: null,
    buyImageInput: null,
    buyButtonTextInput: null
};

/**
 * 初始化编辑器
 */
async function initEditor() {
    // 初始化 Vditor 编辑器
    vditor = new Vditor('vditor', {
        height: 500,
        mode: 'sv', // 默认为分屏预览模式
        theme: 'classic',
        lang: 'zh_CN',
        placeholder: '开始编写你的文章...',
        toolbar: [
            {
                name: 'qmoji',
                tip: '插入 QQ 表情',
                icon: '<svg viewBox="0 0 32 32" width="16" height="16"><path d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14 14-6.2 14-14S23.8 2 16 2zm0 26C9.4 28 4 22.6 4 16S9.4 4 16 4s12 5.4 12 12-5.4 12-12 12z"/><path d="M12 14c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm8 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/><path d="M16 20c-2.3 0-4.3 1.5-5.1 3.7-.2.5.2 1.1.7 1.1h8.8c.5 0 .9-.6.7-1.1C20.3 21.5 18.3 20 16 20z"/></svg>',
                click: () => {
                    toggleQmojiPicker(true);
                }
            },
            'headings',
            'bold',
            'italic',
            'strike',
            'link',
            '|',
            'list',
            'ordered-list',
            'check',
            'outdent',
            'indent',
            '|',
            'quote',
            'line',
            'code',
            'inline-code',
            'insert-before',
            'insert-after',
            '|',
            'upload',
            'table',
            '|',
            'undo',
            'redo',
            '|',
            'fullscreen',
            'preview',
            'outline',
            'help',
            '|',
            'edit-mode'
        ],
        emoji: {
            // 自定义表情面板
            parse: true,
            // 使用自定义的QQ表情数据
            custom: async () => {
                const emojis = await loadQmojiData();
                if (!emojis || emojis.length === 0) {
                    console.error('无法加载QQ表情数据');
                    return {};
                }
                
                // 按类型分组表情
                const groupedEmojis = groupEmojisByType(emojis);
                
                // 创建表情映射
                const emojiMap = {};
                
                // 添加普通表情
                groupedEmojis.normal.forEach(emoji => {
                    const emojiName = emoji.describe.startsWith('/') ? emoji.describe.substring(1) : emoji.describe;
                    emojiMap[emojiName] = {
                        url: getEmojiThumbUrl(emoji),
                        title: emojiName,
                        content: `{{< qq-emoji "${emojiName}" >}}`
                    };
                });
                
                // 添加超级表情
                groupedEmojis.super.forEach(emoji => {
                    const emojiName = emoji.describe.startsWith('/') ? emoji.describe.substring(1) : emoji.describe;
                    emojiMap[emojiName] = {
                        url: getEmojiThumbUrl(emoji),
                        title: emojiName,
                        content: `{{< qq-emoji "${emojiName}" >}}`
                    };
                });
                
                return emojiMap;
            }
        },
        cache: {
            enable: true,
            id: 'hugo-editor'
        },
        preview: {
            hljs: {
                enable: true,
                style: 'github'
            },
            math: {
                inlineDigit: true,
                engine: 'KaTeX'
            },
            /**
             * 自定义预览渲染
             * @param {string} value - Markdown 内容
             * @returns {string} 渲染后的 HTML
             */
            markdown: (value) => {
                // 移除 Front Matter 以防止在预览中显示（支持 +++ 和 --- 格式）
                const frontMatterRegex = /^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/;
                let contentWithoutFrontMatter = value.replace(frontMatterRegex, '');
                
                // 修复链接和图片的 undefined 问题 - 更全面的处理
                // 处理各种形式的 undefined 前缀
                contentWithoutFrontMatter = contentWithoutFrontMatter.replace(/undefined+/g, '');
                
                // 处理图片链接中的 undefined
                contentWithoutFrontMatter = contentWithoutFrontMatter.replace(
                    /!\[([^\]]*)\]\(undefined*([^)]*?)\)/g, 
                    '![$1]($2)'
                );
                
                // 处理普通链接中的 undefined
                contentWithoutFrontMatter = contentWithoutFrontMatter.replace(
                    /\[([^\]]*)\]\(undefined*([^)]*?)\)/g, 
                    '[$1]($2)'
                );
                
                // 处理 HTML 图片标签中的 undefined
                contentWithoutFrontMatter = contentWithoutFrontMatter.replace(
                    /src=["']undefined*([^"']*?)["']/g, 
                    'src="$1"'
                );
                
                // 处理 HTML 链接标签中的 undefined
                contentWithoutFrontMatter = contentWithoutFrontMatter.replace(
                    /href=["']undefined*([^"']*?)["']/g, 
                    'href="$1"'
                );
                
                // 清理可能的双斜杠问题
                contentWithoutFrontMatter = contentWithoutFrontMatter.replace(/([^:])\/{2,}/g, '$1/');
                
                return contentWithoutFrontMatter;
            }
        },
        upload: {
            accept: 'image/*',
            handler: (files) => {
                // 自定义上传逻辑：不使用 base64，而是使用文件名引用
                const file = files[0];
                
                // 检查文件是否存在
                if (!file) {
                    console.error('未选择文件');
                    return;
                }
                
                // 获取文章标题作为文件夹名
                const articleTitle = elements.titleInput.value || 'untitled';
                const folderName = articleTitle.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase();
                // 根据新的目录结构：post/post_name/img_name.png
                const imagePath = `${folderName}/${file.name}`;
                
                // 创建文件对象用于保存（实际项目中这里应该上传文件到服务器）
                const fileData = {
                    name: file.name,
                    path: imagePath,
                    folder: folderName,
                    size: file.size,
                    type: file.type,
                    lastModified: file.lastModified
                };
                
                try {
                    // 保存文件信息到本地存储（模拟上传过程）
                    const uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles') || '[]');
                    uploadedFiles.push(fileData);
                    localStorage.setItem('uploadedFiles', JSON.stringify(uploadedFiles));
                    
                    // 插入图片引用
                    vditor.insertValue(`![${file.name}](${imagePath})`);
                    
                    // 显示提示
                    console.log(`图片将保存到: ${imagePath}`);
                } catch (error) {
                    console.error('保存文件信息时出错:', error);
                    // 在实际应用中，这里应该显示用户友好的错误消息
                }
                
                // 在实际应用中，这里应该：
                // 1. 上传文件到服务器
                // 2. 返回真实的URL
                // 3. 处理上传错误
            }
        },
        after: () => {
            // 编辑器初始化完成后的回调
            loadSavedContent();
        }
    });

    // 初始化 QQ 表情数据
    await initQmojiPicker();
}

/**
 * 初始化 QQ 表情选择器
 */
async function initQmojiPicker() {
    // 加载表情数据
    const emojis = await loadQmojiData();
    if (!emojis || emojis.length === 0) {
        console.error('无法加载QQ表情数据');
        return;
    }

    // 按类型分组表情
    const groupedEmojis = groupEmojisByType(emojis);
    
    // 清空现有内容
    elements.qmojiPickerContent.innerHTML = '';
    
    // 创建分类标签
    const tabContainer = document.createElement('div');
    tabContainer.className = 'qmoji-tabs';
    tabContainer.innerHTML = `
        <button class="qmoji-tab active" data-tab="normal">普通表情</button>
        <button class="qmoji-tab" data-tab="super">超级表情</button>
    `;
    elements.qmojiPickerContent.appendChild(tabContainer);
    
    // 创建表情容器
    const normalContainer = document.createElement('div');
    normalContainer.className = 'qmoji-container active';
    normalContainer.dataset.tabContent = 'normal';
    
    const superContainer = document.createElement('div');
    superContainer.className = 'qmoji-container';
    superContainer.dataset.tabContent = 'super';
    superContainer.style.display = 'none';
    
    // 添加普通表情
    groupedEmojis.normal.forEach(emoji => {
        const emojiItem = createEmojiItem(emoji);
        normalContainer.appendChild(emojiItem);
    });
    
    // 添加超级表情
    groupedEmojis.super.forEach(emoji => {
        const emojiItem = createEmojiItem(emoji);
        superContainer.appendChild(emojiItem);
    });
    
    // 添加到DOM
    elements.qmojiPickerContent.appendChild(normalContainer);
    elements.qmojiPickerContent.appendChild(superContainer);
    
    // 添加标签切换事件
    const tabs = tabContainer.querySelectorAll('.qmoji-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 更新标签状态
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // 更新内容显示
            const tabName = tab.dataset.tab;
            document.querySelectorAll('.qmoji-container').forEach(container => {
                if (container.dataset.tabContent === tabName) {
                    container.style.display = 'grid';
                } else {
                    container.style.display = 'none';
                }
            });
        });
    });
}

/**
 * 创建表情项
 * @param {Object} emoji 表情对象
 * @returns {HTMLElement} 表情项元素
 */
function createEmojiItem(emoji) {
    const emojiItem = document.createElement('div');
    emojiItem.className = 'qmoji-item tooltip tooltip-top';
    emojiItem.setAttribute('data-tip', emoji.describe.startsWith('/') ? emoji.describe.substring(1) : emoji.describe);
    
    // 创建表情图片
    const img = document.createElement('img');
    img.src = getEmojiThumbUrl(emoji);
    img.alt = emoji.describe;
    img.title = emoji.describe;
    img.className = 'w-8 h-8 object-contain';
    emojiItem.appendChild(img);
    
    // 添加悬停事件以显示提示
    let tooltipTimeout;
    emojiItem.addEventListener('mouseenter', () => {
        tooltipTimeout = setTimeout(() => {
            // 使用daisyUI的tooltip类
            emojiItem.classList.add('tooltip-open');
        }, 500); // 悬停500ms后显示提示
    });
    
    emojiItem.addEventListener('mouseleave', () => {
        clearTimeout(tooltipTimeout);
        emojiItem.classList.remove('tooltip-open');
    });
    
    // 确保提示位置正确
    emojiItem.addEventListener('mousemove', (e) => {
        // 可以根据需要调整提示位置
    });
    
    // 添加点击事件
    emojiItem.addEventListener('click', () => {
        insertEmojiToEditor(emoji);
    });
    
    return emojiItem;
}

/**
 * 将表情插入到编辑器
 * @param {Object} emoji 表情对象
 */
function insertEmojiToEditor(emoji) {
    const shortcode = getEmojiShortcode(emoji);
    vditor.insertValue(shortcode);
    toggleQmojiPicker(false);
}

/**
 * 切换 QQ 表情选择器显示状态
 * @param {boolean} show 是否显示
 */
function toggleQmojiPicker(show) {
    if (show === undefined) {
        show = elements.qmojiPicker.classList.contains('hidden');
    }
    
    if (show) {
        elements.qmojiPicker.classList.remove('hidden');
        // 添加 modal-open 类以显示模态框
        elements.qmojiPicker.classList.add('modal-open');
    } else {
        elements.qmojiPicker.classList.add('hidden');
        // 移除 modal-open 类以隐藏模态框
        elements.qmojiPicker.classList.remove('modal-open');
    }
}

/**
 * 切换 Front Matter 编辑器显示状态
 */
function toggleFrontMatter() {
    frontMatterVisible = !frontMatterVisible;
    
    if (frontMatterVisible) {
        elements.frontMatterEditor.style.display = 'block';
    } else {
        elements.frontMatterEditor.style.display = 'none';
    }
}

/**
 * 从编辑器内容中提取 Front Matter
 * @returns {Object} Front Matter 对象
 */
function extractFrontMatter(content) {
    // 支持 +++ 和 --- 两种格式的 Front Matter
    const frontMatterRegex = /^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/;
    const match = content.match(frontMatterRegex);
    
    if (!match) return {};
    
    const frontMatterText = match[1];
    const frontMatter = {};
    
    // 简单解析 YAML 格式的 Front Matter
    const lines = frontMatterText.split(/\r?\n/);
    for (const line of lines) {
        const colonIndex = line.indexOf(':');
        if (colonIndex === -1) continue;
        
        const key = line.substring(0, colonIndex).trim();
        let value = line.substring(colonIndex + 1).trim();
        
        // 处理引号
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith('\'') && value.endsWith('\'')))
        {
            value = value.substring(1, value.length - 1);
        }
        
        // 处理数组
        if (value.startsWith('[') && value.endsWith(']')) {
            const items = value.substring(1, value.length - 1).split(',');
            frontMatter[key] = items.map(item => item.trim());
        } else {
            frontMatter[key] = value;
        }
    }
    
    return frontMatter;
}

/**
 * 从 Front Matter 对象生成 YAML 文本
 * @param {Object} frontMatter Front Matter 对象
 * @returns {string} YAML 文本
 */
function generateFrontMatterYaml(frontMatter) {
    const lines = [];
    
    for (const [key, value] of Object.entries(frontMatter)) {
        if (value === undefined || value === null || value === '') continue;
        
        if (Array.isArray(value)) {
            if (value.length === 0) continue;
            lines.push(`${key}: [${value.map(item => `"${item}"`).join(', ')}]`);
        } else if (typeof value === 'string') {
            // 如果字符串包含特殊字符，添加引号
            if (/[:\[\]{}\r\n,&*#?|\-<>=!%@\\]/.test(value)) {
                lines.push(`${key}: "${value.replace(/"/g, '\\"')}"`);
            } else {
                lines.push(`${key}: ${value}`);
            }
        } else {
            lines.push(`${key}: ${value}`);
        }
    }
    
    return `+++
${lines.join('\n')}
+++

`;
}

/**
 * 更新编辑器中的 Front Matter
 */
function updateFrontMatter() {
    // 获取当前编辑器内容
    const content = vditor.getValue();
    
    // 构建新的 Front Matter
    const frontMatter = {
        title: elements.titleInput.value,
        date: elements.dateInput.value ? 
            // 确保日期为东八区时间
            new Date(elements.dateInput.value + '+08:00').toISOString() : 
            // 当前时间也使用东八区
            new Date(Date.now() + (8 * 60 * 60 * 1000)).toISOString(),
        draft: elements.draftSelect.value === 'true',
        weight: parseInt(elements.weightInput.value) || 0
    };
    
    // 添加作者字段（如果存在）
    if (elements.authorInput.value.trim()) {
        frontMatter.author = elements.authorInput.value.trim();
    }
    
    // 添加状态分类字段（如果存在）
    if (elements.statusCateInput.value.trim()) {
        frontMatter.statusCate = elements.statusCateInput.value.trim();
    }
    
    // 添加分类链接字段（如果存在）
    if (elements.categoryLinkInput.value.trim()) {
        frontMatter.categoryLink = elements.categoryLinkInput.value.trim();
    }
    
    // 添加购买相关字段（如果存在）
    if (elements.buyLinkInput.value.trim()) {
        frontMatter.buy = true; // 如果有购买链接，则设置buy为true
        frontMatter.buyLink = elements.buyLinkInput.value.trim();
    }
    
    if (elements.buyNameInput.value.trim()) {
        frontMatter.buyName = elements.buyNameInput.value.trim();
    }
    
    if (elements.buyInfoInput.value.trim()) {
        frontMatter.buyInfo = elements.buyInfoInput.value.trim();
    }
    
    if (elements.buyImageInput.value.trim()) {
        frontMatter.buyImage = elements.buyImageInput.value.trim();
    }
    
    if (elements.buyButtonTextInput.value.trim()) {
        frontMatter.buyButtonText = elements.buyButtonTextInput.value.trim();
    }
    
    // 处理标签、分类和关键词
    if (elements.tagsInput.value.trim()) {
        frontMatter.tags = elements.tagsInput.value.split(',').map(tag => tag.trim());
    }
    
    if (elements.categoriesInput.value.trim()) {
        frontMatter.categories = elements.categoriesInput.value.split(',').map(category => category.trim());
    }
    
    if (elements.keywordsInput.value.trim()) {
        frontMatter.keywords = elements.keywordsInput.value.split(',').map(keyword => keyword.trim());
    }
    
    // 生成 YAML
    const frontMatterYaml = generateFrontMatterYaml(frontMatter);
    
    // 替换或添加 Front Matter（支持 +++ 和 --- 格式）
    const frontMatterRegex = /^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/;
    if (frontMatterRegex.test(content)) {
        vditor.setValue(content.replace(frontMatterRegex, frontMatterYaml));
    } else {
        vditor.setValue(frontMatterYaml + content);
    }
}

/**
 * 从编辑器内容更新 Front Matter 表单
 */
function updateFrontMatterForm() {
    const content = vditor.getValue();
    const frontMatter = extractFrontMatter(content);
    
    // 更新表单字段
    elements.titleInput.value = frontMatter.title || '';
    
    if (frontMatter.date) {
        // 将 ISO 日期转换为本地日期时间格式
        const date = new Date(frontMatter.date);
        elements.dateInput.value = date.toISOString().slice(0, 16);
    }
    
    elements.draftSelect.value = frontMatter.draft ? 'true' : 'false';
    elements.authorInput.value = frontMatter.author || '';
    elements.tagsInput.value = Array.isArray(frontMatter.tags) ? frontMatter.tags.join(', ') : (frontMatter.tags || '');
    elements.categoriesInput.value = Array.isArray(frontMatter.categories) ? frontMatter.categories.join(', ') : (frontMatter.categories || '');
    elements.keywordsInput.value = Array.isArray(frontMatter.keywords) ? frontMatter.keywords.join(', ') : (frontMatter.keywords || '');
    elements.weightInput.value = frontMatter.weight || 0;
    elements.statusCateInput.value = frontMatter.statusCate || '';
    elements.categoryLinkInput.value = frontMatter.categoryLink || '';
    elements.buyLinkInput.value = frontMatter.buyLink || '';
    elements.buyNameInput.value = frontMatter.buyName || '';
    elements.buyInfoInput.value = frontMatter.buyInfo || '';
    elements.buyImageInput.value = frontMatter.buyImage || '';
    elements.buyButtonTextInput.value = frontMatter.buyButtonText || '';
}

/**
 * 保存内容到本地存储
 */
function saveContent() {
    // 更新 Front Matter
    updateFrontMatter();
    
    // 获取完整内容（包含 Front Matter）
    const fullContent = vditor.getValue();
    
    // 保存到本地存储
    localStorage.setItem('hugo-editor-content', fullContent);
    
    // 移除编辑器中的 Front Matter，只保留文章内容
    const contentWithoutFrontMatter = fullContent.replace(/^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/, '');
    vditor.setValue(contentWithoutFrontMatter);
    
    // 显示保存成功提示
    alert('内容已保存到本地！Front Matter 已保存但不在编辑区显示。');
}

/**
 * 保存文章到本地文件系统（新的文件管理方案）
 */
function saveArticleToFile() {
    // 更新 Front Matter
    updateFrontMatter();
    
    // 获取完整内容（包含 Front Matter）
    const fullContent = vditor.getValue();
    
    // 获取文章标题作为文件名
    const frontMatter = extractFrontMatter(fullContent);
    const articleTitle = frontMatter.title || 'untitled';
    const folderName = articleTitle.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase();
    const fileName = `${folderName}.md`;
    
    // 创建 Blob
    const blob = new Blob([fullContent], { type: 'text/markdown' });
    
    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    
    // 清理
    setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, 0);
    
    // 移除编辑器中的 Front Matter，只保留文章内容
    const contentWithoutFrontMatter = fullContent.replace(/^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/, '');
    vditor.setValue(contentWithoutFrontMatter);
    
    console.log(`文章已保存为: ${fileName}`);
    console.log('建议的目录结构:');
    console.log(`post/${folderName}/`);
    console.log(`├── ${fileName}`);
    console.log(`└── [图片文件...]`);
}

/**
 * 使用文件系统API直接修改本地文件（需要用户授权）
 */
let currentFileHandle = null;

async function openFileWithFileSystemAPI() {
    try {
        // 检查浏览器是否支持 File System Access API
        if (!('showOpenFilePicker' in window)) {
            alert('您的浏览器不支持文件系统API，请使用 Chrome 86+ 或 Edge 86+');
            return;
        }

        // 打开文件选择器
        const [fileHandle] = await window.showOpenFilePicker({
            types: [{
                description: 'Markdown files',
                accept: {
                    'text/markdown': ['.md', '.markdown']
                }
            }],
            multiple: false
        });

        // 保存文件句柄
        currentFileHandle = fileHandle;

        // 读取文件内容
        const file = await fileHandle.getFile();
        const content = await file.text();

        // 解析 Front Matter
        const frontMatter = extractFrontMatter(content);

        // 移除 Front Matter 后的内容
        const contentWithoutFrontMatter = content.replace(/^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/, '');

        // 更新编辑器内容（不包含 Front Matter）
        vditor.setValue(contentWithoutFrontMatter);

        // 更新 Front Matter 表单（与 importArticle 相同的逻辑）
        updateFrontMatterFormFromData(frontMatter);

        console.log('文件已打开:', fileHandle.name);
        alert(`文件 "${fileHandle.name}" 已打开，现在您可以直接修改并保存到原文件。`);

    } catch (error) {
        if (error.name !== 'AbortError') {
            console.error('打开文件失败:', error);
            alert('打开文件失败，请检查浏览器权限设置。');
        }
    }
}

async function saveFileWithFileSystemAPI() {
    try {
        // 更新 Front Matter
        updateFrontMatter();

        // 获取完整内容（包含 Front Matter）
        const fullContent = vditor.getValue();

        if (!currentFileHandle) {
            // 如果没有打开的文件，则创建新文件
            await saveAsNewFile(fullContent);
            return;
        }

        // 创建可写流
        const writable = await currentFileHandle.createWritable();

        // 写入内容
        await writable.write(fullContent);

        // 关闭流
        await writable.close();

        // 移除编辑器中的 Front Matter，只保留文章内容
        const contentWithoutFrontMatter = fullContent.replace(/^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/, '');
        vditor.setValue(contentWithoutFrontMatter);

        console.log('文件已保存:', currentFileHandle.name);
        alert(`文件 "${currentFileHandle.name}" 已成功保存！`);

    } catch (error) {
        console.error('保存文件失败:', error);
        alert('保存文件失败，请检查文件权限。');
    }
}

/**
 * 保存为新文件
 */
async function saveAsNewFile(content) {
    try {
        // 检查浏览器是否支持 File System Access API
        if (!('showSaveFilePicker' in window)) {
            // 如果不支持，则使用传统下载方式
            saveArticleToFile();
            return;
        }

        // 从 Front Matter 中获取文件名
        const frontMatter = extractFrontMatter(content);
        const suggestedName = (frontMatter.title || 'untitled')
            .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-')
            .toLowerCase() + '.md';

        // 打开保存文件对话框
        const fileHandle = await window.showSaveFilePicker({
            types: [{
                description: 'Markdown files',
                accept: {
                    'text/markdown': ['.md', '.markdown']
                }
            }],
            suggestedName: suggestedName
        });

        // 保存文件句柄
        currentFileHandle = fileHandle;

        // 创建可写流
        const writable = await fileHandle.createWritable();

        // 写入内容
        await writable.write(content);

        // 关闭流
        await writable.close();

        // 移除编辑器中的 Front Matter，只保留文章内容
        const contentWithoutFrontMatter = content.replace(/^(\+\+\+|---)\s*[\r\n]+((?:.|\s)*?)\s*(\+\+\+|---)\s*[\r\n]+/, '');
        vditor.setValue(contentWithoutFrontMatter);

        console.log('新文件已保存:', fileHandle.name);
        alert(`新文件 "${fileHandle.name}" 已成功保存！`);

    } catch (error) {
        if (error.name !== 'AbortError') {
            console.error('保存新文件失败:', error);
            alert('保存新文件失败，请检查浏览器权限设置。');
        }
    }
}

/**
 * 从 Front Matter 数据更新表单（提取公共逻辑）
 */
function updateFrontMatterFormFromData(frontMatter) {
    // 清空所有表单字段
    elements.titleInput.value = '';
    elements.dateInput.value = '';
    elements.draftSelect.value = 'false';
    elements.authorInput.value = '';
    elements.weightInput.value = '0';
    elements.tagsInput.value = '';
    elements.categoriesInput.value = '';
    elements.keywordsInput.value = '';
    elements.statusCateInput.value = '';
    elements.categoryLinkInput.value = '';
    elements.buyLinkInput.value = '';
    elements.buyNameInput.value = '';
    elements.buyInfoInput.value = '';
    elements.buyImageInput.value = '';
    elements.buyButtonTextInput.value = '';
    
    // 更新 Front Matter 表单
    if (frontMatter.title) elements.titleInput.value = frontMatter.title;
    if (frontMatter.date) {
        // 处理日期时区问题，确保显示东八区时间
        const date = new Date(frontMatter.date);
        // 转换为东八区时间 (UTC+8)
        const utc8Date = new Date(date.getTime() + (8 * 60 * 60 * 1000));
        elements.dateInput.value = utc8Date.toISOString().slice(0, 16);
    }
    if (frontMatter.draft !== undefined) {
        elements.draftSelect.value = frontMatter.draft.toString();
    }
    if (frontMatter.author) elements.authorInput.value = frontMatter.author;
    if (frontMatter.weight !== undefined) elements.weightInput.value = frontMatter.weight.toString();
    
    // 处理数组字段
    if (frontMatter.tags && Array.isArray(frontMatter.tags)) {
        elements.tagsInput.value = frontMatter.tags.join(', ');
    }
    if (frontMatter.categories && Array.isArray(frontMatter.categories)) {
        elements.categoriesInput.value = frontMatter.categories.join(', ');
    }
    if (frontMatter.keywords && Array.isArray(frontMatter.keywords)) {
        elements.keywordsInput.value = frontMatter.keywords.join(', ');
    }
    
    // 处理其他字段
    if (frontMatter.status_cate) elements.statusCateInput.value = frontMatter.status_cate;
    if (frontMatter.category_link) elements.categoryLinkInput.value = frontMatter.category_link;
    if (frontMatter.buy_link) elements.buyLinkInput.value = frontMatter.buy_link;
    if (frontMatter.buy_name) elements.buyNameInput.value = frontMatter.buy_name;
    if (frontMatter.buy_info) elements.buyInfoInput.value = frontMatter.buy_info;
    if (frontMatter.buy_image) elements.buyImageInput.value = frontMatter.buy_image;
    if (frontMatter.buy_button_text) elements.buyButtonTextInput.value = frontMatter.buy_button_text;
}

/**
 * 从本地存储加载内容
 */
function loadSavedContent() {
    const savedContent = localStorage.getItem('hugo-editor-content');
    if (savedContent) {
        vditor.setValue(savedContent);
        updateFrontMatterForm();
    }
}

/**
 * 导入文章
 */
function importArticle() {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.md,.markdown';
    fileInput.style.display = 'none';
    
    fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            
            // 解析 Front Matter
            const frontMatter = extractFrontMatter(content);
            
            // 移除 Front Matter 后的内容
            const contentWithoutFrontMatter = content.replace(/^\+\+\+[\s\S]*?\+\+\+\s*/, '').replace(/^---[\s\S]*?---\s*/, '');
            
            // 更新编辑器内容（不包含 Front Matter）
            vditor.setValue(contentWithoutFrontMatter);
            
            // 使用公共函数更新 Front Matter 表单
            updateFrontMatterFormFromData(frontMatter);
            
            console.log('文章导入成功:', file.name);
        };
        
        reader.readAsText(file, 'utf-8');
        
        // 清理文件输入
        document.body.removeChild(fileInput);
    });
    
    // 添加到页面并触发点击
    document.body.appendChild(fileInput);
    fileInput.click();
}

/**
 * 初始化事件监听
 */
function initEventListeners() {
    // 文件系统API按钮
    elements.openFileBtn.addEventListener('click', openFileWithFileSystemAPI);
    elements.saveFileBtn.addEventListener('click', saveFileWithFileSystemAPI);
    
    // 保存按钮
    elements.saveBtn.addEventListener('click', saveContent);
    
    // 保存为文件按钮
    elements.saveToFileBtn.addEventListener('click', saveArticleToFile);
    
    // 导出按钮
    // 导出按钮事件监听器已移除
    
    // 导入按钮
    elements.importBtn.addEventListener('click', importArticle);
    
    // 切换 Front Matter 按钮
    elements.toggleFrontMatterBtn.addEventListener('click', toggleFrontMatter);
    
    // 切换 QQ 表情选择器按钮
    elements.toggleQmojiBtn.addEventListener('click', () => toggleQmojiPicker());
    
    // 关闭 QQ 表情选择器按钮
    elements.closeQmojiBtn.addEventListener('click', () => toggleQmojiPicker(false));
    
    // 底部关闭 QQ 表情选择器按钮
    if (elements.closeQmojiBtnBottom) {
        elements.closeQmojiBtnBottom.addEventListener('click', () => toggleQmojiPicker(false));
    }
    
    // 监听编辑器内容变化，更新 Front Matter 表单
    let debounceTimer;
    vditor.on('input', () => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(updateFrontMatterForm, 500);
    });
}

/**
 * 初始化应用
 */
function init() {
    // 获取 DOM 元素
    elements.frontMatterEditor = document.getElementById('frontMatterEditor');
    elements.qmojiPicker = document.getElementById('qmojiPicker');
    elements.qmojiPickerContent = document.querySelector('.qmoji-picker-content');
    
    // 按钮
    elements.openFileBtn = document.getElementById('openFileBtn');
    elements.saveFileBtn = document.getElementById('saveFileBtn');
    elements.saveBtn = document.getElementById('saveBtn');
    elements.saveToFileBtn = document.getElementById('saveToFileBtn');
    // elements.exportBtn 引用已移除
    elements.importBtn = document.getElementById('importBtn');
    elements.toggleFrontMatterBtn = document.getElementById('toggleFrontMatterBtn');
    elements.toggleQmojiBtn = document.getElementById('toggleQmojiBtn');
    elements.closeQmojiBtn = document.getElementById('closeQmojiBtn');
    
    // Front Matter 字段
    elements.titleInput = document.getElementById('title');
    elements.dateInput = document.getElementById('date');
    elements.draftSelect = document.getElementById('draft');
    elements.authorInput = document.getElementById('author');
    elements.tagsInput = document.getElementById('tags');
    elements.categoriesInput = document.getElementById('categories');
    elements.keywordsInput = document.getElementById('keywords');
    elements.weightInput = document.getElementById('weight');
    elements.statusCateInput = document.getElementById('statusCate');
    elements.categoryLinkInput = document.getElementById('categoryLink');
    elements.buyLinkInput = document.getElementById('buyLink');
    elements.buyNameInput = document.getElementById('buyName');
    elements.buyInfoInput = document.getElementById('buyInfo');
    elements.buyImageInput = document.getElementById('buyImage');
    elements.buyButtonTextInput = document.getElementById('buyButtonText');
    
    // 设置默认日期
    elements.dateInput.value = new Date().toISOString().slice(0, 16);
    
    // 初始化编辑器
    initEditor();
    
    // 初始化事件监听
    initEventListeners();
}

// 当 DOM 加载完成后初始化应用
document.addEventListener('DOMContentLoaded', init);

// 添加 CSS 样式
const style = document.createElement('style');
style.textContent = `
.qmoji-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
}

.qmoji-tab {
    padding: 8px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    margin-right: 10px;
    color: #555;
}

.qmoji-tab.active {
    border-bottom-color: #3498db;
    color: #3498db;
}

.qmoji-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
}
`;
document.head.appendChild(style);