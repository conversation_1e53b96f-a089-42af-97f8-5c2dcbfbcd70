/**
 * QQ表情数据加载和处理
 */

// 表情数据缓存
let qmojiData = null;

/**
 * 从本地JSON文件加载QQ表情数据
 * @returns {Promise<Array>} 表情数据数组
 */
async function loadQmojiData() {
    if (qmojiData !== null) {
        return qmojiData;
    }

    // 尝试从GitHub加载
    try {
        const githubResponse = await fetch(
            "https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/mapping.json"
        );
        if (!githubResponse.ok) {
            throw new Error(
                `GitHub HTTP error! status: ${githubResponse.status}`
            );
        }

        qmojiData = await githubResponse.json();
        return qmojiData;
    } catch (githubError) {
        console.error("从GitHub加载QQ表情数据失败:", githubError);
        return [];
    }
}

/**
 * 获取表情的缩略图URL
 * @param {Object} emoji 表情对象
 * @returns {string} 缩略图URL
 */
function getEmojiThumbUrl(emoji) {
    const emojiId = emoji.emojiId;
    return `https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/${emojiId}/thumb.png`;
}

/**
 * 获取表情的Hugo Shortcode
 * @param {Object} emoji 表情对象
 * @returns {string} Hugo Shortcode
 */
function getEmojiShortcode(emoji) {
    const describe = emoji.describe;
    const emojiName = describe.startsWith("/")
        ? describe.substring(1)
        : describe;
    return `{{< qq-emoji "${emojiName}" >}}`;
}

/**
 * 获取表情的图片URL
 * @param {Object} emoji 表情对象
 * @returns {string} 图片URL
 */
function getEmojiImageUrl(emoji) {
    // 使用 thumb.png 作为预览图片
    return `https://qzonestyle.gtimg.cn/qzone/em/${emoji.AniStickerPackId}/${emoji.AniStickerId}/thumb.png`;
}

/**
 * 根据表情名称查找表情对象
 * @param {string} emojiName 表情名称
 * @param {Array} emojis 表情数组
 * @returns {Object|null} 表情对象或null
 */
function findEmojiByName(emojiName, emojis) {
    return emojis.find(emoji => {
        const describe = emoji.describe;
        const name = describe.startsWith("/") ? describe.substring(1) : describe;
        return name === emojiName;
    }) || null;
}

/**
 * 按类型对表情进行分组
 * @param {Array} emojis 表情数组
 * @returns {Object} 分组后的表情
 */
function groupEmojisByType(emojis) {
    const groups = {
        normal: [], // 普通表情 (emojiType: 0 或 1)
        super: [], // 超级表情 (emojiType: 2)
    };

    // 用于跟踪已处理的表情名称，避免重复
    const processedNames = new Set();

    for (const emoji of emojis) {
        const describe = emoji.describe;
        if (!describe) continue;

        // 去掉前缀斜杠用作键名
        const keyName = describe.startsWith("/")
            ? describe.substring(1)
            : describe;

        // 跳过已处理的表情名称
        if (processedNames.has(keyName)) continue;
        processedNames.add(keyName);

        // 根据类型分组
        if (emoji.emojiType === 2) {
            groups.super.push(emoji);
        } else {
            groups.normal.push(emoji);
        }
    }

    return groups;
}
