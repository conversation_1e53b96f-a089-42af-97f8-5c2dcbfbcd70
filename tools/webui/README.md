# Hugo 文章编辑器 - 支持 QQ 表情

这是一个轻量级的 Hugo 文章编辑器，专为博客作者设计，支持 Hugo Front Matter 编辑和 QQ 表情选择器。

## 功能特点

- 基于 [Vditor](https://github.com/Vanessa219/vditor) 的 Markdown 编辑器
- Hugo Front Matter 支持（标题、日期、标签、分类等）
- QQ 表情选择器，支持普通表情和超级表情
- 本地保存功能
- Markdown 导出功能
- 完全静态，无需后端

## 使用方法

1. 在浏览器中打开 `index.html` 文件
2. 使用 Front Matter 编辑器设置文章元数据
3. 在 Markdown 编辑器中编写文章内容
4. 点击 "QQ 表情选择器" 按钮插入 QQ 表情
5. 点击 "保存" 按钮将内容保存到本地浏览器
6. 点击 "导出 Markdown" 下载文章文件

## QQ 表情使用说明

1. 点击界面上的 "QQ 表情选择器" 按钮打开表情选择面板
2. 选择 "普通表情" 或 "超级表情" 标签切换表情类型
3. 将鼠标悬停在表情上可以查看表情名称
4. 点击表情将自动在编辑器中插入对应的 Hugo Shortcode

## 技术说明

- 表情数据来源于 [Spixed/Qmoji](https://github.com/Spixed/Qmoji) 项目
- 表情在编辑器中以 Hugo Shortcode 形式插入：`{{< qq-emoji "表情名" >}}`
- 表情图片使用 CDN 加速：`https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/`

## 本地开发

如果需要修改或扩展功能，可以编辑以下文件：

- `index.html` - 主界面 HTML 结构
- `css/style.css` - 样式表
- `js/main.js` - 主要功能实现
- `js/qmoji-data.js` - QQ 表情数据加载和处理

## 注意事项

- 本工具使用浏览器的 localStorage 保存内容，清除浏览器数据会导致内容丢失
- 建议定期导出 Markdown 文件以备份内容
- 如果表情数据加载失败，会尝试从 GitHub 获取最新数据