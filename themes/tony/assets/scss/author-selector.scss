/* 作者选择器样式 */

// 变量定义
$primary-gold: #FFD700;
$light-gold: #FFFBEA;
$shadow-gold: rgba(255, 215, 0, 0.18);
$shadow-gold-hover: rgba(255, 215, 0, 0.28);
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 多作者布局容器
.multi-author-layout {
  .desktop-layout {
    display: flex;
    max-width: 1200px;
    margin: 80px auto 0;
    padding: 0 20px;
    gap: 40px;
    
    @media (max-width: 768px) {
      display: none;
    }
  }
  
  .mobile-layout {
    display: none;
    
    @media (max-width: 768px) {
      display: block;
      padding: 20px;
      margin-top: 60px;
    }
  }
}

// 左侧作者区域
.author-section {
  flex: 2;
  min-height: 500px;
  position: relative;
}

// 右侧文章区域
.articles-section {
  flex: 3;
  min-height: 500px;
}

// 作者选择器区域
.author-selector-area {
  position: sticky;
  top: 20px;
  width: 100%;
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px; // 增加与下方按钮的距离
  z-index: 10;

  // 触发区域指示器（可选，用于调试）
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    border-radius: 50%;
    z-index: 1;
    pointer-events: none;
    // border: 1px dashed rgba(255, 215, 0, 0.3); // 调试时可启用
  }
}

// 正(n+1)边形作者选择器
.author-polygon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  opacity: 0;
  transition: opacity $transition-normal;

  &.active {
    opacity: 1;
  }
}

// 作者头像
.author-avatar {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all $transition-fast;
  object-fit: cover;
  
  &:hover {
    transform: scale(1.15);
    box-shadow: 0 0 12px $primary-gold;
    border-color: $primary-gold;
  }
  
  &.selected {
    border-color: $primary-gold;
    box-shadow: 0 0 16px $shadow-gold-hover;
  }
}

// 中心作者信息
.author-center-info {
  position: relative;
  text-align: center;
  z-index: 10;
}

.center-avatar-container {
  margin-bottom: 20px;
}

.center-avatar-img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #fff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  transition: all $transition-normal;
  display: block;
  margin: 0 auto;
}

.center-author-details {
  max-width: 280px;
  margin: 0 auto;
}

.author-nickname {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 10px;
  color: #333;
}

.author-bio {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 15px;
  line-height: 1.4;
}

.author-social {
  display: flex;
  justify-content: center;
  gap: 15px;
  
  a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #f5f5f5;
    color: #666;
    text-decoration: none;
    transition: all $transition-fast;
    
    &:hover {
      background: $primary-gold;
      color: #fff;
      transform: translateY(-2px);
    }
    
    i {
      font-size: 1.1rem;
    }
  }
}

// 移动端样式
.mobile-current-author {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin: 10px 20px 20px;
  cursor: pointer;
  transition: all $transition-fast;
  position: relative;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

.mobile-current-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
}

.mobile-current-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

// 移动端弹窗
.mobile-author-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  
  &.active {
    display: block;
  }
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 16px;
  padding: 0;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #eee;
  
  h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
  }
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all $transition-fast;
  
  &:hover {
    background: #f5f5f5;
    color: #333;
  }
}

.modal-body {
  padding: 20px;
}

.mobile-author-polygon {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 25px;
  justify-items: center;
  padding: 10px;
}

.mobile-author-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 12px;
  cursor: pointer;
  transition: all $transition-fast;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &.selected {
    background: lighten($primary-gold, 35%);
  }
  
  img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 8px;
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.mobile-author-name {
  font-size: 0.85rem;
  color: #333;
  text-align: center;
}

// 响应式调整
@media (max-width: 1024px) {
  .desktop-layout {
    max-width: 100%;
    gap: 30px;
  }
  
  .author-polygon {
    width: 250px;
    height: 250px;
  }
  
  .author-avatar {
    width: 50px;
    height: 50px;
  }
  
  .center-avatar-img {
    width: 100px;
    height: 100px;
  }
}

// 移动端触发器
.mobile-author-trigger {
  display: none;
  text-align: center;
  padding: 20px;
  cursor: pointer;

  .mobile-trigger-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    object-fit: cover;
    margin-bottom: 10px;
    transition: all $transition-fast;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    }
  }

  .mobile-trigger-name {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
  }
}

@media (max-width: 768px) {
  .mobile-author-trigger {
    display: block;
  }

  .mobile-author-header {
    margin-bottom: 20px;
  }

  // 移动端竖屏优化
  .mobile-current-author {
    margin: 5px 15px 15px;
    padding: 10px 15px;

    .mobile-current-avatar {
      width: 40px;
      height: 40px;
    }

    .mobile-current-name {
      font-size: 0.9rem;
      margin-left: 10px;
    }
  }

  // 移动端弹窗优化
  .modal-content {
    max-width: 95vw;
    max-height: 85vh;
  }

  .mobile-author-polygon {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .mobile-author-item {
    padding: 10px;

    img {
      width: 50px;
      height: 50px;
    }
  }

  .mobile-author-name {
    font-size: 0.8rem;
  }
}

// 移动端横屏优化
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-current-author {
    margin: 5px 10px 10px;
    padding: 8px 12px;

    .mobile-current-avatar {
      width: 35px;
      height: 35px;
    }

    .mobile-current-name {
      font-size: 0.85rem;
      margin-left: 8px;
    }
  }

  .modal-content {
    max-height: 90vh;
  }

  .mobile-author-polygon {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }

  .mobile-author-item {
    padding: 8px;

    img {
      width: 45px;
      height: 45px;
    }
  }
}
