/* 文章卡片样式 */

// 变量定义
$primary-gold: #FFD700;
$light-gold: #FFFBEA;
$shadow-gold: rgba(255, 215, 0, 0.18);
$shadow-gold-hover: rgba(255, 215, 0, 0.28);
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;

// 文章列表容器
.article-list-container {
  width: 100%;
}

// 文章列表
.article-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

// 文章卡片基础样式
.article-list-item {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  // 新拟态风格阴影 - 更明显的层次感
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transition: all $transition-normal;
  position: relative;
  border: 2px solid transparent;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
  
  // 精选文章样式
  &.featured {
    border: 3px solid;
    border-radius: 12px;
    background: #fff;
    position: relative;
    z-index: 1;

    // 边框渐变色流动效果 - 仅边框
    border-image: linear-gradient(180deg,
      #FFFFFF 0%,
      #FFFFFF 30%,
      rgba(255, 215, 0, 0.4) 45%,
      $primary-gold 50%,
      rgba(255, 215, 0, 0.4) 55%,
      #FFFFFF 70%,
      #FFFFFF 100%
    ) 1;
    border-image-slice: 1;

    // 使用伪元素实现动画边框
    &::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      background: linear-gradient(180deg,
        #FFFFFF 0%,
        #FFFFFF 30%,
        rgba(255, 215, 0, 0.4) 45%,
        $primary-gold 50%,
        rgba(255, 215, 0, 0.4) 55%,
        #FFFFFF 70%,
        #FFFFFF 100%
      );
      background-size: 100% 400%;
      animation: flowingBorder 4s ease-in-out infinite;
      border-radius: 12px;
      z-index: -1;
      pointer-events: none;
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: xor;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      padding: 3px;
    }

    &:hover {
      box-shadow: 0 4px 25px rgba(255, 215, 0, 0.2);
      transform: translateY(-3px);
    }
  }
  
  // 隐藏状态（用于过滤）
  &.hidden {
    display: none;
  }
}

// 边框流动动画 - 金色在左下角与左上角之间流动
@keyframes flowingBorder {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

// Pin图标
.pin-icon {
  position: absolute;
  left: 12px;
  top: 12px;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, $primary-gold 30%, #FFFBEA 70%, #FFFFFF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  pointer-events: none;
  z-index: 2;

  i {
    color: #fff;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// 文章底部信息
.article-list-footer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
  font-size: 0.85rem;
  color: #666;
}

.article-list-date {
  color: #666;
}

.article-list-divider {
  color: #ccc;
}

// 作者信息
.article-author {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  text-decoration: none;
  
  &:hover {
    color: #333;
  }
}

.author-mini-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #eee;
}

// 精选标识
.featured-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: $primary-gold;
  font-weight: 500;
  
  i {
    font-size: 0.9rem;
  }
}

// 无限滚动加载指示器
.infinite-scroll-indicator {
  text-align: center;
  margin: 40px 0;
  padding: 20px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;

  .loading-spinner {
    i {
      font-size: 1.2rem;
      animation: spin 1s linear infinite;
    }
  }

  span {
    font-size: 0.9rem;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 无文章提示
.no-articles {
  text-align: center;
  padding: 60px 20px;
  color: #999;
  
  .no-articles-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
  }
  
  p {
    font-size: 1.1rem;
    margin: 0;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .mobile-articles-section {
    .article-list-item {
      padding: 15px;
      margin-bottom: 15px;
      
      &.featured {
        .pin-icon {
          width: 24px;
          height: 24px;
          left: 10px;
          top: 10px;
          
          i {
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .article-list-footer {
    font-size: 0.8rem;
    gap: 6px;
  }
  
  .author-mini-avatar {
    width: 18px;
    height: 18px;
  }
  
  .load-more-btn {
    padding: 10px 20px;
    font-size: 0.85rem;
  }
}

// 文章卡片内容样式增强
.article-list-item {
  h5 {
    color: #333;
    font-weight: 600;
    line-height: 1.4;
    margin: 15px 0 10px;
    
    &:hover {
      color: #007bff;
    }
  }
  
  p {
    color: #666;
    line-height: 1.6;
    margin: 0 0 15px;
  }
  
  // 分类标签样式
  .article-list-type1 {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #666;
    font-style: normal;
  }
  
  .img-cate {
    background: rgba(255, 152, 0, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    
    &:hover {
      background: rgba(255, 152, 0, 0.2);
    }
  }
}

// 状态文章样式
.article-status {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  margin: 10px 0;
}

// 购买相关样式
.buy-list-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 10px 0;
  min-height: 100px;

  .buy-left-img-noborder {
    flex-shrink: 0;

    img {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
    }
  }

  .buy-right-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0; // 防止flex子元素溢出
    gap: 10px;

    > div:first-child {
      flex: 1;
    }

    > div:last-child {
      margin-top: 10px;
    }

    h3 {
      margin: 0 0 8px;
      font-size: 1.1rem;
      color: #333;
      line-height: 1.3;
    }

    p {
      margin: 0 0 10px;
      color: #666;
      font-size: 0.9rem;
      line-height: 1.4;
    }

    a {
      color: #007bff;
      text-decoration: none;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      gap: 4px;

      &:hover {
        text-decoration: underline;
      }

      span {
        display: inline-flex;
        align-items: center;
      }
    }
  }

  // 移动端优化
  @media (max-width: 768px) {
    gap: 12px;
    padding: 12px;

    .buy-left-img-noborder img {
      width: 60px;
      height: 60px;
    }

    .buy-right-info {
      h3 {
        font-size: 1rem;
      }

      p {
        font-size: 0.85rem;
      }
    }
  }
}
