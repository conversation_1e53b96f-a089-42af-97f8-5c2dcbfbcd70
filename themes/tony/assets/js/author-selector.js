/**
 * 作者选择器交互逻辑
 * 使用原生JavaScript，无jQuery依赖
 */

class AuthorSelector {
  constructor() {
    this.currentAuthor = window.currentAuthor || 'featured'; // 默认选择精选文章
    this.authorsData = window.authorsData || {};
    this.articlesData = window.articlesData || [];
    this.isPolygonVisible = false;
    this.isMobile = window.innerWidth <= 768;
    this.isLoading = false;
    this.articlesPerPage = 10;
    this.displayedArticles = this.articlesPerPage;
    
    this.init();
  }
  
  init() {
    this.setupEventListeners();
    this.setupPolygonLayout();
    this.filterArticles(this.currentAuthor);
    this.setupInfiniteScroll();
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      const wasMobile = this.isMobile;
      this.isMobile = window.innerWidth <= 768;
      
      if (wasMobile !== this.isMobile) {
        this.setupPolygonLayout();
      }
    });
  }
  
  setupEventListeners() {
    // PC端事件
    const authorSelectorArea = document.getElementById('authorSelectorArea');
    const authorPolygon = document.getElementById('authorPolygon');
    const centerInfo = document.getElementById('centerInfo');

    if (authorSelectorArea && !this.isMobile) {
      // 鼠标进入显示选择器
      authorSelectorArea.addEventListener('mouseenter', () => {
        this.showPolygon();
      });

      // 鼠标离开隐藏选择器
      authorSelectorArea.addEventListener('mouseleave', () => {
        this.hidePolygon();
      });

      // 点击中心区域显示/隐藏选择器
      if (centerInfo) {
        centerInfo.addEventListener('click', () => {
          if (this.isPolygonVisible) {
            this.hidePolygon();
          } else {
            this.showPolygon();
          }
        });
      }
    }
    
    // 作者头像点击事件
    if (authorPolygon) {
      const avatars = authorPolygon.querySelectorAll('.author-avatar');
      avatars.forEach(avatar => {
        avatar.addEventListener('click', (e) => {
          e.stopPropagation();
          const authorKey = avatar.dataset.author;
          this.selectAuthor(authorKey);
        });
      });
    }
    
    // 移动端事件
    const mobileAuthorTrigger = document.getElementById('mobileAuthorTrigger');
    const mobileAuthorModal = document.getElementById('mobileAuthorModal');
    const modalOverlay = document.getElementById('modalOverlay');
    const modalClose = document.getElementById('modalClose');

    if (mobileAuthorTrigger && this.isMobile) {
      mobileAuthorTrigger.addEventListener('click', () => {
        this.showMobileModal();
      });
    }
    
    if (modalOverlay) {
      modalOverlay.addEventListener('click', () => {
        this.hideMobileModal();
      });
    }
    
    if (modalClose) {
      modalClose.addEventListener('click', () => {
        this.hideMobileModal();
      });
    }
    
    // 移动端作者选择
    const mobileAuthorItems = document.querySelectorAll('.mobile-author-item');
    mobileAuthorItems.forEach(item => {
      item.addEventListener('click', () => {
        const authorKey = item.dataset.author;
        this.selectAuthor(authorKey);
        this.hideMobileModal();
      });
    });
  }

  setupPolygonLayout() {
    const authorPolygon = document.getElementById('authorPolygon');
    if (!authorPolygon) return;
    
    const avatars = authorPolygon.querySelectorAll('.author-avatar');
    const totalAuthors = avatars.length;
    
    if (totalAuthors === 0) return;
    
    const radius = this.isMobile ? 100 : 120;
    const centerX = this.isMobile ? 125 : 150;
    const centerY = this.isMobile ? 125 : 150;
    
    avatars.forEach((avatar, index) => {
      const angle = (index * 2 * Math.PI) / totalAuthors - Math.PI / 2;
      const x = centerX + radius * Math.cos(angle) - 30;
      const y = centerY + radius * Math.sin(angle) - 30;
      
      avatar.style.left = `${x}px`;
      avatar.style.top = `${y}px`;
      
      // 标记当前选中的作者
      if (avatar.dataset.author === this.currentAuthor) {
        avatar.classList.add('selected');
      } else {
        avatar.classList.remove('selected');
      }
    });
  }

  showPolygon() {
    const authorPolygon = document.getElementById('authorPolygon');
    const centerAuthorDetails = document.getElementById('centerAuthorDetails');

    if (authorPolygon && !this.isMobile) {
      authorPolygon.classList.add('active');
      this.isPolygonVisible = true;

      // 隐藏除中心头像外的作者信息
      if (centerAuthorDetails) {
        centerAuthorDetails.style.opacity = '0';
        centerAuthorDetails.style.visibility = 'hidden';
      }
    }
  }

  hidePolygon() {
    const authorPolygon = document.getElementById('authorPolygon');
    const centerAuthorDetails = document.getElementById('centerAuthorDetails');

    if (authorPolygon && !this.isMobile) {
      authorPolygon.classList.remove('active');
      this.isPolygonVisible = false;

      // 显示作者信息
      if (centerAuthorDetails) {
        centerAuthorDetails.style.opacity = '1';
        centerAuthorDetails.style.visibility = 'visible';
      }
    }
  }
  
  showMobileModal() {
    const modal = document.getElementById('mobileAuthorModal');
    if (modal) {
      modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    }
  }
  
  hideMobileModal() {
    const modal = document.getElementById('mobileAuthorModal');
    if (modal) {
      modal.classList.remove('active');
      document.body.style.overflow = '';
    }
  }
  
  selectAuthor(authorKey) {
    if (authorKey === this.currentAuthor) return;
    
    this.currentAuthor = authorKey;
    window.currentAuthor = authorKey;
    
    // 更新中心作者信息
    this.updateCenterAuthorInfo(authorKey);
    
    // 更新移动端当前作者
    if (this.isMobile) {
      this.updateMobileCurrentAuthor(authorKey);
    }
    
    // 过滤文章
    this.filterArticles(authorKey);
    
    // 更新选中状态
    this.updateSelectedState();
  }
  
  updateCenterAuthorInfo(authorKey) {
    if (authorKey === 'featured') {
      // 精选文章显示
      const centerAvatarImg = document.getElementById('centerAvatarImg');
      const authorNickname = document.getElementById('authorNickname');
      const authorBio = document.getElementById('authorBio');
      const authorSocial = document.getElementById('authorSocial');
      
      if (centerAvatarImg) {
        centerAvatarImg.src = '/site/logo.png';
        centerAvatarImg.alt = '精选文章';
      }
      
      if (authorNickname) {
        authorNickname.textContent = '精选文章';
      }
      
      if (authorBio) {
        authorBio.textContent = '精心挑选的优质内容';
      }
      
      if (authorSocial) {
        authorSocial.innerHTML = '';
      }
      return;
    }
    
    const author = this.authorsData[authorKey];
    if (!author) return;
    
    const centerAvatarImg = document.getElementById('centerAvatarImg');
    const authorNickname = document.getElementById('authorNickname');
    const authorBio = document.getElementById('authorBio');
    const authorSocial = document.getElementById('authorSocial');
    
    if (centerAvatarImg) {
      centerAvatarImg.src = author.avatar;
      centerAvatarImg.alt = author.name;
    }
    
    if (authorNickname) {
      authorNickname.textContent = author.nickname;
    }
    
    if (authorBio) {
      authorBio.textContent = author.bio;
    }
    
    if (authorSocial) {
      let socialHTML = '';
      if (author.github) {
        socialHTML += `<a href="${author.github}" target="_blank" title="GitHub"><i class="ri-github-line"></i></a>`;
      }
      if (author.email) {
        socialHTML += `<a href="mailto:${author.email}" title="Email"><i class="ri-mail-line"></i></a>`;
      }
      if (author.website) {
        socialHTML += `<a href="${author.website}" target="_blank" title="Website"><i class="ri-global-line"></i></a>`;
      }
      // 添加About链接（仅对spixed作者）
      if (authorKey === 'spixed') {
        socialHTML += `<a href="/about" title="About"><i class="ri-user-line"></i></a>`;
      }
      authorSocial.innerHTML = socialHTML;
    }
  }

  updateMobileCurrentAuthor(authorKey) {
    if (authorKey === 'featured') {
      const mobileTriggerAvatar = document.getElementById('mobileTriggerAvatar');
      const mobileTriggerName = document.getElementById('mobileTriggerName');

      if (mobileTriggerAvatar) {
        mobileTriggerAvatar.src = '/site/logo.png';
        mobileTriggerAvatar.alt = '精选文章';
      }

      if (mobileTriggerName) {
        mobileTriggerName.textContent = '精选文章';
      }
      return;
    }

    const author = this.authorsData[authorKey];
    if (!author) return;

    // 更新移动端触发器
    const mobileTriggerAvatar = document.getElementById('mobileTriggerAvatar');
    const mobileTriggerName = document.getElementById('mobileTriggerName');

    if (mobileTriggerAvatar) {
      mobileTriggerAvatar.src = author.avatar;
      mobileTriggerAvatar.alt = author.name;
    }

    if (mobileTriggerName) {
      mobileTriggerName.textContent = author.nickname;
    }
  }

  updateSelectedState() {
    // 更新PC端选中状态
    const avatars = document.querySelectorAll('.author-avatar');
    avatars.forEach(avatar => {
      if (avatar.dataset.author === this.currentAuthor) {
        avatar.classList.add('selected');
      } else {
        avatar.classList.remove('selected');
      }
    });

    // 更新移动端选中状态
    const mobileItems = document.querySelectorAll('.mobile-author-item');
    mobileItems.forEach(item => {
      if (item.dataset.author === this.currentAuthor) {
        item.classList.add('selected');
      } else {
        item.classList.remove('selected');
      }
    });
  }

  setupInfiniteScroll() {
    // 设置无限滚动
    const articlesSection = document.querySelector('.articles-section');
    const indicator = document.getElementById('infiniteScrollIndicator');

    if (!articlesSection) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.isLoading) {
          this.loadMoreArticles();
        }
      });
    }, {
      rootMargin: '100px'
    });

    // 创建观察目标
    const sentinel = document.createElement('div');
    sentinel.style.height = '1px';
    articlesSection.appendChild(sentinel);

    observer.observe(sentinel);
  }

  loadMoreArticles() {
    if (this.isLoading) return;

    this.isLoading = true;
    const indicator = document.getElementById('infiniteScrollIndicator');

    if (indicator) {
      indicator.style.display = 'flex';
    }

    // 模拟加载延迟
    setTimeout(() => {
      this.displayedArticles += this.articlesPerPage;
      this.filterArticles(this.currentAuthor);

      this.isLoading = false;
      if (indicator) {
        indicator.style.display = 'none';
      }
    }, 500);
  }

  filterArticles(authorKey) {
    const articleItems = document.querySelectorAll('.article-list-item');
    const noArticles = document.getElementById('noArticles');
    let visibleCount = 0;
    let matchingItems = [];

    articleItems.forEach(item => {
      const itemAuthor = item.dataset.author;
      const itemFeatured = item.dataset.featured === 'true';

      let shouldShow = false;

      if (authorKey === 'featured') {
        // 显示所有精选文章
        shouldShow = itemFeatured;
      } else {
        // 显示指定作者的文章
        shouldShow = itemAuthor === authorKey;
      }

      if (shouldShow) {
        matchingItems.push(item);
      }
    });

    // 如果是新的作者选择，重置显示计数
    if (this.currentAuthor !== authorKey) {
      this.displayedArticles = this.articlesPerPage;
    }

    // 隐藏所有文章
    articleItems.forEach(item => {
      item.classList.add('hidden');
    });

    // 显示指定数量的匹配文章
    const articlesToShow = Math.min(this.displayedArticles, matchingItems.length);
    for (let i = 0; i < articlesToShow; i++) {
      matchingItems[i].classList.remove('hidden');
      visibleCount++;
    }

    // 显示/隐藏无文章提示
    if (noArticles) {
      if (visibleCount === 0) {
        noArticles.style.display = 'block';
      } else {
        noArticles.style.display = 'none';
      }
    }

    // 更新当前作者
    this.currentAuthor = authorKey;
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new AuthorSelector();
});
