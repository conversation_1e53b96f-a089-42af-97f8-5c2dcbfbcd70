{{/* QQ表情单个表情shortcode */}}
{{/* 用法: {{< qq-emoji "微笑" >}} */}}

{{- $emojiName := .Get 0 -}}
{{- if not $emojiName -}}
    <span class="qq-emoji-error">❌ 请提供表情名称</span>
{{- else -}}
    {{/* 内嵌表情数据映射 */}}
    {{- if false -}}
        {{/* 这个条件永远不会执行，只是为了语法结构 */}}
        {{- else if eq $emojiName "惊讶" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/0/apng.png" class="qmoji" alt="/惊讶" title="/惊讶" />
        {{- else if eq $emojiName "撇嘴" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/1/apng.png" class="qmoji" alt="/撇嘴" title="/撇嘴" />
        {{- else if eq $emojiName "色" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/2/apng.png" class="qmoji" alt="/色" title="/色" />
        {{- else if eq $emojiName "发呆" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/3/apng.png" class="qmoji" alt="/发呆" title="/发呆" />
        {{- else if eq $emojiName "得意" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/4/apng.png" class="qmoji" alt="/得意" title="/得意" />
        {{- else if eq $emojiName "流泪" -}}
            {{- $uniqueId := "lottie-emoji-5-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/5/lottie.json" title="/流泪"></div>
        {{- else if eq $emojiName "害羞" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/6/apng.png" class="qmoji" alt="/害羞" title="/害羞" />
        {{- else if eq $emojiName "闭嘴" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/7/apng.png" class="qmoji" alt="/闭嘴" title="/闭嘴" />
        {{- else if eq $emojiName "睡" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/8/apng.png" class="qmoji" alt="/睡" title="/睡" />
        {{- else if eq $emojiName "大哭" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/9/apng.png" class="qmoji" alt="/大哭" title="/大哭" />
        {{- else if eq $emojiName "尴尬" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/10/apng.png" class="qmoji" alt="/尴尬" title="/尴尬" />
        {{- else if eq $emojiName "发怒" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/11/apng.png" class="qmoji" alt="/发怒" title="/发怒" />
        {{- else if eq $emojiName "调皮" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/12/apng.png" class="qmoji" alt="/调皮" title="/调皮" />
        {{- else if eq $emojiName "呲牙" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/13/apng.png" class="qmoji" alt="/呲牙" title="/呲牙" />
        {{- else if eq $emojiName "微笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/14/apng.png" class="qmoji" alt="/微笑" title="/微笑" />
        {{- else if eq $emojiName "难过" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/15/apng.png" class="qmoji" alt="/难过" title="/难过" />
        {{- else if eq $emojiName "酷" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/16/apng.png" class="qmoji" alt="/酷" title="/酷" />
        {{- else if eq $emojiName "抓狂" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/18/apng.png" class="qmoji" alt="/抓狂" title="/抓狂" />
        {{- else if eq $emojiName "吐" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/19/apng.png" class="qmoji" alt="/吐" title="/吐" />
        {{- else if eq $emojiName "偷笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/20/apng.png" class="qmoji" alt="/偷笑" title="/偷笑" />
        {{- else if eq $emojiName "可爱" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/21/apng.png" class="qmoji" alt="/可爱" title="/可爱" />
        {{- else if eq $emojiName "白眼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/22/apng.png" class="qmoji" alt="/白眼" title="/白眼" />
        {{- else if eq $emojiName "傲慢" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/23/apng.png" class="qmoji" alt="/傲慢" title="/傲慢" />
        {{- else if eq $emojiName "饥饿" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/24/apng.png" class="qmoji" alt="/饥饿" title="/饥饿" />
        {{- else if eq $emojiName "困" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/25/apng.png" class="qmoji" alt="/困" title="/困" />
        {{- else if eq $emojiName "惊恐" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/26/apng.png" class="qmoji" alt="/惊恐" title="/惊恐" />
        {{- else if eq $emojiName "流汗" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/27/apng.png" class="qmoji" alt="/流汗" title="/流汗" />
        {{- else if eq $emojiName "憨笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/28/apng.png" class="qmoji" alt="/憨笑" title="/憨笑" />
        {{- else if eq $emojiName "悠闲" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/29/apng.png" class="qmoji" alt="/悠闲" title="/悠闲" />
        {{- else if eq $emojiName "奋斗" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/30/apng.png" class="qmoji" alt="/奋斗" title="/奋斗" />
        {{- else if eq $emojiName "咒骂" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/31/apng.png" class="qmoji" alt="/咒骂" title="/咒骂" />
        {{- else if eq $emojiName "疑问" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/32/apng.png" class="qmoji" alt="/疑问" title="/疑问" />
        {{- else if eq $emojiName "嘘" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/33/apng.png" class="qmoji" alt="/嘘" title="/嘘" />
        {{- else if eq $emojiName "晕" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/34/apng.png" class="qmoji" alt="/晕" title="/晕" />
        {{- else if eq $emojiName "折磨" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/35/apng.png" class="qmoji" alt="/折磨" title="/折磨" />
        {{- else if eq $emojiName "衰" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/36/apng.png" class="qmoji" alt="/衰" title="/衰" />
        {{- else if eq $emojiName "骷髅" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/37/apng.png" class="qmoji" alt="/骷髅" title="/骷髅" />
        {{- else if eq $emojiName "敲打" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/38/apng.png" class="qmoji" alt="/敲打" title="/敲打" />
        {{- else if eq $emojiName "再见" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/39/apng.png" class="qmoji" alt="/再见" title="/再见" />
        {{- else if eq $emojiName "发抖" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/41/thumb.png" class="qmoji" alt="/发抖" title="/发抖" />
        {{- else if eq $emojiName "爱情" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/42/thumb.png" class="qmoji" alt="/爱情" title="/爱情" />
        {{- else if eq $emojiName "跳跳" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/43/thumb.png" class="qmoji" alt="/跳跳" title="/跳跳" />
        {{- else if eq $emojiName "猪头" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/46/apng.png" class="qmoji" alt="/猪头" title="/猪头" />
        {{- else if eq $emojiName "拥抱" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/49/apng.png" class="qmoji" alt="/拥抱" title="/拥抱" />
        {{- else if eq $emojiName "蛋糕" -}}
            {{- $uniqueId := "lottie-emoji-53-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/53/lottie.json" title="/蛋糕"></div>
        {{- else if eq $emojiName "炸弹" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/55/thumb.png" class="qmoji" alt="/炸弹" title="/炸弹" />
        {{- else if eq $emojiName "刀" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/56/apng.png" class="qmoji" alt="/刀" title="/刀" />
        {{- else if eq $emojiName "便便" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/59/apng.png" class="qmoji" alt="/便便" title="/便便" />
        {{- else if eq $emojiName "咖啡" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/60/apng.png" class="qmoji" alt="/咖啡" title="/咖啡" />
        {{- else if eq $emojiName "玫瑰" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/63/apng.png" class="qmoji" alt="/玫瑰" title="/玫瑰" />
        {{- else if eq $emojiName "凋谢" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/64/apng.png" class="qmoji" alt="/凋谢" title="/凋谢" />
        {{- else if eq $emojiName "爱心" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/66/apng.png" class="qmoji" alt="/爱心" title="/爱心" />
        {{- else if eq $emojiName "心碎" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/67/apng.png" class="qmoji" alt="/心碎" title="/心碎" />
        {{- else if eq $emojiName "太阳" -}}
            {{- $uniqueId := "lottie-emoji-74-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/74/lottie.json" title="/太阳"></div>
        {{- else if eq $emojiName "月亮" -}}
            {{- $uniqueId := "lottie-emoji-75-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/75/lottie.json" title="/月亮"></div>
        {{- else if eq $emojiName "赞" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/76/apng.png" class="qmoji" alt="/赞" title="/赞" />
        {{- else if eq $emojiName "踩" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/77/apng.png" class="qmoji" alt="/踩" title="/踩" />
        {{- else if eq $emojiName "握手" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/78/apng.png" class="qmoji" alt="/握手" title="/握手" />
        {{- else if eq $emojiName "胜利" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/79/apng.png" class="qmoji" alt="/胜利" title="/胜利" />
        {{- else if eq $emojiName "飞吻" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/85/thumb.png" class="qmoji" alt="/飞吻" title="/飞吻" />
        {{- else if eq $emojiName "怄火" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/86/thumb.png" class="qmoji" alt="/怄火" title="/怄火" />
        {{- else if eq $emojiName "西瓜" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/89/apng.png" class="qmoji" alt="/西瓜" title="/西瓜" />
        {{- else if eq $emojiName "冷汗" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/96/apng.png" class="qmoji" alt="/冷汗" title="/冷汗" />
        {{- else if eq $emojiName "擦汗" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/97/apng.png" class="qmoji" alt="/擦汗" title="/擦汗" />
        {{- else if eq $emojiName "抠鼻" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/98/apng.png" class="qmoji" alt="/抠鼻" title="/抠鼻" />
        {{- else if eq $emojiName "鼓掌" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/99/apng.png" class="qmoji" alt="/鼓掌" title="/鼓掌" />
        {{- else if eq $emojiName "糗大了" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/100/apng.png" class="qmoji" alt="/糗大了" title="/糗大了" />
        {{- else if eq $emojiName "坏笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/101/apng.png" class="qmoji" alt="/坏笑" title="/坏笑" />
        {{- else if eq $emojiName "左哼哼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/102/apng.png" class="qmoji" alt="/左哼哼" title="/左哼哼" />
        {{- else if eq $emojiName "右哼哼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/103/apng.png" class="qmoji" alt="/右哼哼" title="/右哼哼" />
        {{- else if eq $emojiName "哈欠" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/104/apng.png" class="qmoji" alt="/哈欠" title="/哈欠" />
        {{- else if eq $emojiName "鄙视" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/105/apng.png" class="qmoji" alt="/鄙视" title="/鄙视" />
        {{- else if eq $emojiName "委屈" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/106/apng.png" class="qmoji" alt="/委屈" title="/委屈" />
        {{- else if eq $emojiName "快哭了" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/107/apng.png" class="qmoji" alt="/快哭了" title="/快哭了" />
        {{- else if eq $emojiName "阴险" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/108/apng.png" class="qmoji" alt="/阴险" title="/阴险" />
        {{- else if eq $emojiName "左亲亲" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/109/apng.png" class="qmoji" alt="/左亲亲" title="/左亲亲" />
        {{- else if eq $emojiName "吓" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/110/apng.png" class="qmoji" alt="/吓" title="/吓" />
        {{- else if eq $emojiName "可怜" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/111/apng.png" class="qmoji" alt="/可怜" title="/可怜" />
        {{- else if eq $emojiName "菜刀" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/112/apng.png" class="qmoji" alt="/菜刀" title="/菜刀" />
        {{- else if eq $emojiName "示爱" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/116/apng.png" class="qmoji" alt="/示爱" title="/示爱" />
        {{- else if eq $emojiName "抱拳" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/118/apng.png" class="qmoji" alt="/抱拳" title="/抱拳" />
        {{- else if eq $emojiName "勾引" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/119/apng.png" class="qmoji" alt="/勾引" title="/勾引" />
        {{- else if eq $emojiName "拳头" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/120/apng.png" class="qmoji" alt="/拳头" title="/拳头" />
        {{- else if eq $emojiName "差劲" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/121/apng.png" class="qmoji" alt="/差劲" title="/差劲" />
        {{- else if eq $emojiName "爱你" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/122/apng.png" class="qmoji" alt="/爱你" title="/爱你" />
        {{- else if eq $emojiName "NO" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/123/apng.png" class="qmoji" alt="/NO" title="/NO" />
        {{- else if eq $emojiName "OK" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/124/apng.png" class="qmoji" alt="/OK" title="/OK" />
        {{- else if eq $emojiName "转圈" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/125/thumb.png" class="qmoji" alt="/转圈" title="/转圈" />
        {{- else if eq $emojiName "挥手" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/129/thumb.png" class="qmoji" alt="/挥手" title="/挥手" />
        {{- else if eq $emojiName "鞭炮" -}}
            {{- $uniqueId := "lottie-emoji-137-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/137/lottie.json" title="/鞭炮"></div>
        {{- else if eq $emojiName "喝彩" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/144/apng.png" class="qmoji" alt="/喝彩" title="/喝彩" />
        {{- else if eq $emojiName "爆筋" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/146/apng.png" class="qmoji" alt="/爆筋" title="/爆筋" />
        {{- else if eq $emojiName "棒棒糖" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/147/apng.png" class="qmoji" alt="/棒棒糖" title="/棒棒糖" />
        {{- else if eq $emojiName "喝奶" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/148/thumb.png" class="qmoji" alt="/喝奶" title="/喝奶" />
        {{- else if eq $emojiName "手枪" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/169/apng.png" class="qmoji" alt="/手枪" title="/手枪" />
        {{- else if eq $emojiName "茶" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/171/apng.png" class="qmoji" alt="/茶" title="/茶" />
        {{- else if eq $emojiName "眨眼睛" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/172/apng.png" class="qmoji" alt="/眨眼睛" title="/眨眼睛" />
        {{- else if eq $emojiName "泪奔" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/173/apng.png" class="qmoji" alt="/泪奔" title="/泪奔" />
        {{- else if eq $emojiName "无奈" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/174/apng.png" class="qmoji" alt="/无奈" title="/无奈" />
        {{- else if eq $emojiName "卖萌" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/175/apng.png" class="qmoji" alt="/卖萌" title="/卖萌" />
        {{- else if eq $emojiName "小纠结" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/176/apng.png" class="qmoji" alt="/小纠结" title="/小纠结" />
        {{- else if eq $emojiName "喷血" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/177/apng.png" class="qmoji" alt="/喷血" title="/喷血" />
        {{- else if eq $emojiName "斜眼笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/178/apng.png" class="qmoji" alt="/斜眼笑" title="/斜眼笑" />
        {{- else if eq $emojiName "doge" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/179/apng.png" class="qmoji" alt="/doge" title="/doge" />
        {{- else if eq $emojiName "惊喜" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/180/apng.png" class="qmoji" alt="/惊喜" title="/惊喜" />
        {{- else if eq $emojiName "戳一戳" -}}
            {{- $uniqueId := "lottie-emoji-181-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/181/lottie.json" title="/戳一戳"></div>
        {{- else if eq $emojiName "笑哭" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/182/apng.png" class="qmoji" alt="/笑哭" title="/笑哭" />
        {{- else if eq $emojiName "我最美" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/183/apng.png" class="qmoji" alt="/我最美" title="/我最美" />
        {{- else if eq $emojiName "羊驼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/185/apng.png" class="qmoji" alt="/羊驼" title="/羊驼" />
        {{- else if eq $emojiName "幽灵" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/187/apng.png" class="qmoji" alt="/幽灵" title="/幽灵" />
        {{- else if eq $emojiName "大笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/193/apng.png" class="qmoji" alt="/大笑" title="/大笑" />
        {{- else if eq $emojiName "不开心" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/194/apng.png" class="qmoji" alt="/不开心" title="/不开心" />
        {{- else if eq $emojiName "呃" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/198/apng.png" class="qmoji" alt="/呃" title="/呃" />
        {{- else if eq $emojiName "求求" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/200/apng.png" class="qmoji" alt="/求求" title="/求求" />
        {{- else if eq $emojiName "点赞" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/201/apng.png" class="qmoji" alt="/点赞" title="/点赞" />
        {{- else if eq $emojiName "无聊" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/202/apng.png" class="qmoji" alt="/无聊" title="/无聊" />
        {{- else if eq $emojiName "托脸" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/203/apng.png" class="qmoji" alt="/托脸" title="/托脸" />
        {{- else if eq $emojiName "吃" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/204/apng.png" class="qmoji" alt="/吃" title="/吃" />
        {{- else if eq $emojiName "害怕" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/206/apng.png" class="qmoji" alt="/害怕" title="/害怕" />
        {{- else if eq $emojiName "飙泪" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/210/apng.png" class="qmoji" alt="/飙泪" title="/飙泪" />
        {{- else if eq $emojiName "我不看" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/211/apng.png" class="qmoji" alt="/我不看" title="/我不看" />
        {{- else if eq $emojiName "托腮" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/212/apng.png" class="qmoji" alt="/托腮" title="/托腮" />
        {{- else if eq $emojiName "啵啵" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/214/thumb.png" class="qmoji" alt="/啵啵" title="/啵啵" />
        {{- else if eq $emojiName "糊脸" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/215/thumb.png" class="qmoji" alt="/糊脸" title="/糊脸" />
        {{- else if eq $emojiName "拍头" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/216/thumb.png" class="qmoji" alt="/拍头" title="/拍头" />
        {{- else if eq $emojiName "扯一扯" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/217/thumb.png" class="qmoji" alt="/扯一扯" title="/扯一扯" />
        {{- else if eq $emojiName "舔一舔" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/218/thumb.png" class="qmoji" alt="/舔一舔" title="/舔一舔" />
        {{- else if eq $emojiName "蹭一蹭" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/219/thumb.png" class="qmoji" alt="/蹭一蹭" title="/蹭一蹭" />
        {{- else if eq $emojiName "顶呱呱" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/221/thumb.png" class="qmoji" alt="/顶呱呱" title="/顶呱呱" />
        {{- else if eq $emojiName "抱抱" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/222/thumb.png" class="qmoji" alt="/抱抱" title="/抱抱" />
        {{- else if eq $emojiName "暴击" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/223/thumb.png" class="qmoji" alt="/暴击" title="/暴击" />
        {{- else if eq $emojiName "开枪" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/224/thumb.png" class="qmoji" alt="/开枪" title="/开枪" />
        {{- else if eq $emojiName "撩一撩" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/225/thumb.png" class="qmoji" alt="/撩一撩" title="/撩一撩" />
        {{- else if eq $emojiName "拍桌" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/226/thumb.png" class="qmoji" alt="/拍桌" title="/拍桌" />
        {{- else if eq $emojiName "拍手" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/227/thumb.png" class="qmoji" alt="/拍手" title="/拍手" />
        {{- else if eq $emojiName "干杯" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/229/thumb.png" class="qmoji" alt="/干杯" title="/干杯" />
        {{- else if eq $emojiName "嘲讽" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/230/thumb.png" class="qmoji" alt="/嘲讽" title="/嘲讽" />
        {{- else if eq $emojiName "哼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/231/thumb.png" class="qmoji" alt="/哼" title="/哼" />
        {{- else if eq $emojiName "佛系" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/232/thumb.png" class="qmoji" alt="/佛系" title="/佛系" />
        {{- else if eq $emojiName "掐一掐" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/233/thumb.png" class="qmoji" alt="/掐一掐" title="/掐一掐" />
        {{- else if eq $emojiName "颤抖" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/235/thumb.png" class="qmoji" alt="/颤抖" title="/颤抖" />
        {{- else if eq $emojiName "偷看" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/237/thumb.png" class="qmoji" alt="/偷看" title="/偷看" />
        {{- else if eq $emojiName "扇脸" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/238/thumb.png" class="qmoji" alt="/扇脸" title="/扇脸" />
        {{- else if eq $emojiName "原谅" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/239/thumb.png" class="qmoji" alt="/原谅" title="/原谅" />
        {{- else if eq $emojiName "喷脸" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/240/thumb.png" class="qmoji" alt="/喷脸" title="/喷脸" />
        {{- else if eq $emojiName "生日快乐" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/241/thumb.png" class="qmoji" alt="/生日快乐" title="/生日快乐" />
        {{- else if eq $emojiName "甩头" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/243/thumb.png" class="qmoji" alt="/甩头" title="/甩头" />
        {{- else if eq $emojiName "扔狗" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/244/thumb.png" class="qmoji" alt="/扔狗" title="/扔狗" />
        {{- else if eq $emojiName "脑阔疼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/262/apng.png" class="qmoji" alt="/脑阔疼" title="/脑阔疼" />
        {{- else if eq $emojiName "沧桑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/263/apng.png" class="qmoji" alt="/沧桑" title="/沧桑" />
        {{- else if eq $emojiName "捂脸" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/264/apng.png" class="qmoji" alt="/捂脸" title="/捂脸" />
        {{- else if eq $emojiName "辣眼睛" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/265/apng.png" class="qmoji" alt="/辣眼睛" title="/辣眼睛" />
        {{- else if eq $emojiName "哦哟" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/266/apng.png" class="qmoji" alt="/哦哟" title="/哦哟" />
        {{- else if eq $emojiName "头秃" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/267/apng.png" class="qmoji" alt="/头秃" title="/头秃" />
        {{- else if eq $emojiName "问号脸" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/268/apng.png" class="qmoji" alt="/问号脸" title="/问号脸" />
        {{- else if eq $emojiName "暗中观察" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/269/apng.png" class="qmoji" alt="/暗中观察" title="/暗中观察" />
        {{- else if eq $emojiName "emm" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/270/apng.png" class="qmoji" alt="/emm" title="/emm" />
        {{- else if eq $emojiName "吃瓜" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/271/apng.png" class="qmoji" alt="/吃瓜" title="/吃瓜" />
        {{- else if eq $emojiName "呵呵哒" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/272/apng.png" class="qmoji" alt="/呵呵哒" title="/呵呵哒" />
        {{- else if eq $emojiName "我酸了" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/273/apng.png" class="qmoji" alt="/我酸了" title="/我酸了" />
        {{- else if eq $emojiName "汪汪" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/277/apng.png" class="qmoji" alt="/汪汪" title="/汪汪" />
        {{- else if eq $emojiName "汗" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/278/thumb.png" class="qmoji" alt="/汗" title="/汗" />
        {{- else if eq $emojiName "无眼笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/281/apng.png" class="qmoji" alt="/无眼笑" title="/无眼笑" />
        {{- else if eq $emojiName "敬礼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/282/apng.png" class="qmoji" alt="/敬礼" title="/敬礼" />
        {{- else if eq $emojiName "狂笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/283/apng.png" class="qmoji" alt="/狂笑" title="/狂笑" />
        {{- else if eq $emojiName "面无表情" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/284/apng.png" class="qmoji" alt="/面无表情" title="/面无表情" />
        {{- else if eq $emojiName "摸鱼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/285/apng.png" class="qmoji" alt="/摸鱼" title="/摸鱼" />
        {{- else if eq $emojiName "魔鬼笑" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/286/apng.png" class="qmoji" alt="/魔鬼笑" title="/魔鬼笑" />
        {{- else if eq $emojiName "哦" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/287/apng.png" class="qmoji" alt="/哦" title="/哦" />
        {{- else if eq $emojiName "请" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/288/apng.png" class="qmoji" alt="/请" title="/请" />
        {{- else if eq $emojiName "睁眼" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/289/apng.png" class="qmoji" alt="/睁眼" title="/睁眼" />
        {{- else if eq $emojiName "敲开心" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/290/thumb.png" class="qmoji" alt="/敲开心" title="/敲开心" />
        {{- else if eq $emojiName "让我康康" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/292/thumb.png" class="qmoji" alt="/让我康康" title="/让我康康" />
        {{- else if eq $emojiName "摸锦鲤" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/293/apng.png" class="qmoji" alt="/摸锦鲤" title="/摸锦鲤" />
        {{- else if eq $emojiName "期待" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/294/apng.png" class="qmoji" alt="/期待" title="/期待" />
        {{- else if eq $emojiName "拿到红包" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/295/apng.png" class="qmoji" alt="/拿到红包" title="/拿到红包" />
        {{- else if eq $emojiName "拜谢" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/297/apng.png" class="qmoji" alt="/拜谢" title="/拜谢" />
        {{- else if eq $emojiName "元宝" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/298/apng.png" class="qmoji" alt="/元宝" title="/元宝" />
        {{- else if eq $emojiName "牛啊" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/299/apng.png" class="qmoji" alt="/牛啊" title="/牛啊" />
        {{- else if eq $emojiName "胖三斤" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/300/apng.png" class="qmoji" alt="/胖三斤" title="/胖三斤" />
        {{- else if eq $emojiName "好闪" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/301/apng.png" class="qmoji" alt="/好闪" title="/好闪" />
        {{- else if eq $emojiName "左拜年" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/302/apng.png" class="qmoji" alt="/左拜年" title="/左拜年" />
        {{- else if eq $emojiName "右拜年" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/303/apng.png" class="qmoji" alt="/右拜年" title="/右拜年" />
        {{- else if eq $emojiName "右亲亲" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/305/apng.png" class="qmoji" alt="/右亲亲" title="/右亲亲" />
        {{- else if eq $emojiName "牛气冲天" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/306/apng.png" class="qmoji" alt="/牛气冲天" title="/牛气冲天" />
        {{- else if eq $emojiName "喵喵" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/307/apng.png" class="qmoji" alt="/喵喵" title="/喵喵" />
        {{- else if eq $emojiName "打call" -}}
            {{- $uniqueId := "lottie-emoji-311-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/311/lottie.json" title="/打call"></div>
        {{- else if eq $emojiName "变形" -}}
            {{- $uniqueId := "lottie-emoji-312-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/312/lottie.json" title="/变形"></div>
        {{- else if eq $emojiName "仔细分析" -}}
            {{- $uniqueId := "lottie-emoji-314-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/314/lottie.json" title="/仔细分析"></div>
        {{- else if eq $emojiName "菜汪" -}}
            {{- $uniqueId := "lottie-emoji-317-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/317/lottie.json" title="/菜汪"></div>
        {{- else if eq $emojiName "崇拜" -}}
            {{- $uniqueId := "lottie-emoji-318-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/318/lottie.json" title="/崇拜"></div>
        {{- else if eq $emojiName "比心" -}}
            {{- $uniqueId := "lottie-emoji-319-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/319/lottie.json" title="/比心"></div>
        {{- else if eq $emojiName "庆祝" -}}
            {{- $uniqueId := "lottie-emoji-320-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/320/lottie.json" title="/庆祝"></div>
        {{- else if eq $emojiName "拒绝" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/322/apng.png" class="qmoji" alt="/拒绝" title="/拒绝" />
        {{- else if eq $emojiName "嫌弃" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/323/apng.png" class="qmoji" alt="/嫌弃" title="/嫌弃" />
        {{- else if eq $emojiName "吃糖" -}}
            {{- $uniqueId := "lottie-emoji-324-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/324/lottie.json" title="/吃糖"></div>
        {{- else if eq $emojiName "惊吓" -}}
            {{- $uniqueId := "lottie-emoji-325-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/325/lottie.json" title="/惊吓"></div>
        {{- else if eq $emojiName "生气" -}}
            {{- $uniqueId := "lottie-emoji-326-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/326/lottie.json" title="/生气"></div>
        {{- else if eq $emojiName "举牌牌" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/332/apng.png" class="qmoji" alt="/举牌牌" title="/举牌牌" />
        {{- else if eq $emojiName "烟花" -}}
            {{- $uniqueId := "lottie-emoji-333-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/333/lottie.json" title="/烟花"></div>
        {{- else if eq $emojiName "虎虎生威" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/334/apng.png" class="qmoji" alt="/虎虎生威" title="/虎虎生威" />
        {{- else if eq $emojiName "豹富" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/336/apng.png" class="qmoji" alt="/豹富" title="/豹富" />
        {{- else if eq $emojiName "花朵脸" -}}
            {{- $uniqueId := "lottie-emoji-337-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/337/lottie.json" title="/花朵脸"></div>
        {{- else if eq $emojiName "我想开了" -}}
            {{- $uniqueId := "lottie-emoji-338-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/338/lottie.json" title="/我想开了"></div>
        {{- else if eq $emojiName "舔屏" -}}
            {{- $uniqueId := "lottie-emoji-339-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/339/lottie.json" title="/舔屏"></div>
        {{- else if eq $emojiName "打招呼" -}}
            {{- $uniqueId := "lottie-emoji-341-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/341/lottie.json" title="/打招呼"></div>
        {{- else if eq $emojiName "酸Q" -}}
            {{- $uniqueId := "lottie-emoji-342-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/342/lottie.json" title="/酸Q"></div>
        {{- else if eq $emojiName "我方了" -}}
            {{- $uniqueId := "lottie-emoji-343-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/343/lottie.json" title="/我方了"></div>
        {{- else if eq $emojiName "大怨种" -}}
            {{- $uniqueId := "lottie-emoji-344-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/344/lottie.json" title="/大怨种"></div>
        {{- else if eq $emojiName "红包多多" -}}
            {{- $uniqueId := "lottie-emoji-345-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/345/lottie.json" title="/红包多多"></div>
        {{- else if eq $emojiName "你真棒棒" -}}
            {{- $uniqueId := "lottie-emoji-346-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/346/lottie.json" title="/你真棒棒"></div>
        {{- else if eq $emojiName "大展宏兔" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/347/apng.png" class="qmoji" alt="/大展宏兔" title="/大展宏兔" />
        {{- else if eq $emojiName "福萝卜" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/348/apng.png" class="qmoji" alt="/福萝卜" title="/福萝卜" />
        {{- else if eq $emojiName "坚强" -}}
            {{- $uniqueId := "lottie-emoji-349-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/349/lottie.json" title="/坚强"></div>
        {{- else if eq $emojiName "贴贴" -}}
            {{- $uniqueId := "lottie-emoji-350-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/350/lottie.json" title="/贴贴"></div>
        {{- else if eq $emojiName "敲敲" -}}
            {{- $uniqueId := "lottie-emoji-351-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/351/lottie.json" title="/敲敲"></div>
        {{- else if eq $emojiName "咦" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/352/apng.png" class="qmoji" alt="/咦" title="/咦" />
        {{- else if eq $emojiName "拜托" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/353/apng.png" class="qmoji" alt="/拜托" title="/拜托" />
        {{- else if eq $emojiName "尊嘟假嘟" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/354/apng.png" class="qmoji" alt="/尊嘟假嘟" title="/尊嘟假嘟" />
        {{- else if eq $emojiName "耶" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/355/apng.png" class="qmoji" alt="/耶" title="/耶" />
        {{- else if eq $emojiName "666" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/356/apng.png" class="qmoji" alt="/666" title="/666" />
        {{- else if eq $emojiName "裂开" -}}
            <img src="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/357/apng.png" class="qmoji" alt="/裂开" title="/裂开" />
        {{- else if eq $emojiName "亲亲" -}}
            {{- $uniqueId := "lottie-emoji-360-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/360/lottie.json" title="/亲亲"></div>
        {{- else if eq $emojiName "狗狗笑哭" -}}
            {{- $uniqueId := "lottie-emoji-361-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/361/lottie.json" title="/狗狗笑哭"></div>
        {{- else if eq $emojiName "好兄弟" -}}
            {{- $uniqueId := "lottie-emoji-362-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/362/lottie.json" title="/好兄弟"></div>
        {{- else if eq $emojiName "狗狗可怜" -}}
            {{- $uniqueId := "lottie-emoji-363-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/363/lottie.json" title="/狗狗可怜"></div>
        {{- else if eq $emojiName "超级赞" -}}
            {{- $uniqueId := "lottie-emoji-364-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/364/lottie.json" title="/超级赞"></div>
        {{- else if eq $emojiName "狗狗生气" -}}
            {{- $uniqueId := "lottie-emoji-365-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/365/lottie.json" title="/狗狗生气"></div>
        {{- else if eq $emojiName "芒狗" -}}
            {{- $uniqueId := "lottie-emoji-366-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/366/lottie.json" title="/芒狗"></div>
        {{- else if eq $emojiName "狗狗疑问" -}}
            {{- $uniqueId := "lottie-emoji-367-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/367/lottie.json" title="/狗狗疑问"></div>
        {{- else if eq $emojiName "奥特笑哭" -}}
            {{- $uniqueId := "lottie-emoji-368-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/368/lottie.json" title="/奥特笑哭"></div>
        {{- else if eq $emojiName "彩虹" -}}
            {{- $uniqueId := "lottie-emoji-369-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/369/lottie.json" title="/彩虹"></div>
        {{- else if eq $emojiName "祝贺" -}}
            {{- $uniqueId := "lottie-emoji-370-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/370/lottie.json" title="/祝贺"></div>
        {{- else if eq $emojiName "冒泡" -}}
            {{- $uniqueId := "lottie-emoji-371-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/371/lottie.json" title="/冒泡"></div>
        {{- else if eq $emojiName "气呼呼" -}}
            {{- $uniqueId := "lottie-emoji-372-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/372/lottie.json" title="/气呼呼"></div>
        {{- else if eq $emojiName "忙" -}}
            {{- $uniqueId := "lottie-emoji-373-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/373/lottie.json" title="/忙"></div>
        {{- else if eq $emojiName "波波流泪" -}}
            {{- $uniqueId := "lottie-emoji-374-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/374/lottie.json" title="/波波流泪"></div>
        {{- else if eq $emojiName "超级鼓掌" -}}
            {{- $uniqueId := "lottie-emoji-375-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/375/lottie.json" title="/超级鼓掌"></div>
        {{- else if eq $emojiName "跺脚" -}}
            {{- $uniqueId := "lottie-emoji-376-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/376/lottie.json" title="/跺脚"></div>
        {{- else if eq $emojiName "嗨" -}}
            {{- $uniqueId := "lottie-emoji-377-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/377/lottie.json" title="/嗨"></div>
        {{- else if eq $emojiName "企鹅笑哭" -}}
            {{- $uniqueId := "lottie-emoji-378-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/378/lottie.json" title="/企鹅笑哭"></div>
        {{- else if eq $emojiName "企鹅流泪" -}}
            {{- $uniqueId := "lottie-emoji-379-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/379/lottie.json" title="/企鹅流泪"></div>
        {{- else if eq $emojiName "真棒" -}}
            {{- $uniqueId := "lottie-emoji-380-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/380/lottie.json" title="/真棒"></div>
        {{- else if eq $emojiName "路过" -}}
            {{- $uniqueId := "lottie-emoji-381-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/381/lottie.json" title="/路过"></div>
        {{- else if eq $emojiName "emo" -}}
            {{- $uniqueId := "lottie-emoji-382-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/382/lottie.json" title="/emo"></div>
        {{- else if eq $emojiName "企鹅爱心" -}}
            {{- $uniqueId := "lottie-emoji-383-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/383/lottie.json" title="/企鹅爱心"></div>
        {{- else if eq $emojiName "晚安" -}}
            {{- $uniqueId := "lottie-emoji-384-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/384/lottie.json" title="/晚安"></div>
        {{- else if eq $emojiName "太气了" -}}
            {{- $uniqueId := "lottie-emoji-385-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/385/lottie.json" title="/太气了"></div>
        {{- else if eq $emojiName "呜呜呜" -}}
            {{- $uniqueId := "lottie-emoji-386-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/386/lottie.json" title="/呜呜呜"></div>
        {{- else if eq $emojiName "太好笑" -}}
            {{- $uniqueId := "lottie-emoji-387-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/387/lottie.json" title="/太好笑"></div>
        {{- else if eq $emojiName "太头疼" -}}
            {{- $uniqueId := "lottie-emoji-388-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/388/lottie.json" title="/太头疼"></div>
        {{- else if eq $emojiName "太赞了" -}}
            {{- $uniqueId := "lottie-emoji-389-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/389/lottie.json" title="/太赞了"></div>
        {{- else if eq $emojiName "太头秃" -}}
            {{- $uniqueId := "lottie-emoji-390-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/390/lottie.json" title="/太头秃"></div>
        {{- else if eq $emojiName "太沧桑" -}}
            {{- $uniqueId := "lottie-emoji-391-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/391/lottie.json" title="/太沧桑"></div>
        {{- else if eq $emojiName "略略略" -}}
            {{- $uniqueId := "lottie-emoji-395-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/395/lottie.json" title="/略略略"></div>
        {{- else if eq $emojiName "狼狗" -}}
            {{- $uniqueId := "lottie-emoji-396-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/396/lottie.json" title="/狼狗"></div>
        {{- else if eq $emojiName "抛媚眼" -}}
            {{- $uniqueId := "lottie-emoji-397-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/397/lottie.json" title="/抛媚眼"></div>
        {{- else if eq $emojiName "超级ok" -}}
            {{- $uniqueId := "lottie-emoji-398-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/398/lottie.json" title="/超级ok"></div>
        {{- else if eq $emojiName "tui" -}}
            {{- $uniqueId := "lottie-emoji-399-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/399/lottie.json" title="/tui"></div>
        {{- else if eq $emojiName "快乐" -}}
            {{- $uniqueId := "lottie-emoji-400-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/400/lottie.json" title="/快乐"></div>
        {{- else if eq $emojiName "超级转圈" -}}
            {{- $uniqueId := "lottie-emoji-401-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/401/lottie.json" title="/超级转圈"></div>
        {{- else if eq $emojiName "别说话" -}}
            {{- $uniqueId := "lottie-emoji-402-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/402/lottie.json" title="/别说话"></div>
        {{- else if eq $emojiName "出去玩" -}}
            {{- $uniqueId := "lottie-emoji-403-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/403/lottie.json" title="/出去玩"></div>
        {{- else if eq $emojiName "闪亮登场" -}}
            {{- $uniqueId := "lottie-emoji-404-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/404/lottie.json" title="/闪亮登场"></div>
        {{- else if eq $emojiName "好运来" -}}
            {{- $uniqueId := "lottie-emoji-405-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/405/lottie.json" title="/好运来"></div>
        {{- else if eq $emojiName "姐是女王" -}}
            {{- $uniqueId := "lottie-emoji-406-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/406/lottie.json" title="/姐是女王"></div>
        {{- else if eq $emojiName "我听听" -}}
            {{- $uniqueId := "lottie-emoji-407-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/407/lottie.json" title="/我听听"></div>
        {{- else if eq $emojiName "臭美" -}}
            {{- $uniqueId := "lottie-emoji-408-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/408/lottie.json" title="/臭美"></div>
        {{- else if eq $emojiName "送你花花" -}}
            {{- $uniqueId := "lottie-emoji-409-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/409/lottie.json" title="/送你花花"></div>
        {{- else if eq $emojiName "么么哒" -}}
            {{- $uniqueId := "lottie-emoji-410-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/410/lottie.json" title="/么么哒"></div>
        {{- else if eq $emojiName "一起嗨" -}}
            {{- $uniqueId := "lottie-emoji-411-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/411/lottie.json" title="/一起嗨"></div>
        {{- else if eq $emojiName "开心" -}}
            {{- $uniqueId := "lottie-emoji-412-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/412/lottie.json" title="/开心"></div>
        {{- else if eq $emojiName "摇起来" -}}
            {{- $uniqueId := "lottie-emoji-413-{{ now.UnixNano }}" -}}
            <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://cdn.jsdelivr.net/gh/Spixed/Qmoji@main/res/413/lottie.json" title="/摇起来"></div>
    {{- else -}}
        <span class="qq-emoji-fallback">:/{{ $emojiName }}:</span>
    {{- end -}}
{{- end -}}