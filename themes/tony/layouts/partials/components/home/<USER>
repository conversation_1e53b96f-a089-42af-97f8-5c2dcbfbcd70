<!-- 多作者文章列表组件 -->
<div class="article-list-container" id="articleListContainer">
  <!-- 文章列表 -->
  <ul class="article-list" id="articleList">
    <!-- 所有文章数据，按作者分组 -->
    {{ $allPosts := where .Site.RegularPages "Section" "in" .Site.Params.mainSections }}
    
    <!-- 精选文章 -->
    {{ $featuredPosts := where $allPosts ".Params.featured" "eq" true }}
    {{ range $featuredPosts.ByWeight }}
      <li class="article-list-item reveal index-post-list featured" 
          data-author="{{ .Params.author | default "spixed" }}"
          data-featured="true"
          data-weight="{{ .Params.weight | default 999 }}"
          data-date="{{ .Date.Unix }}">
        <!-- Pin图标 -->
        <div class="pin-icon" title="精选文章">
          <i class="ri-pushpin-2-fill"></i>
        </div>
        
        {{ if (.Params.thumbnail) }}
          <div class="article-list-img-else">
            <div class="article-list-img" style="background-image: url(&quot;{{ .Params.thumbnail }}&quot;);"></div>
            <div class="article-list-img-right">
              {{ partial "components/home/<USER>" . }}
            </div>
          </div>
        {{ else }}
          {{ partial "components/home/<USER>" . }}
        {{ end }}
      </li>
    {{ end }}
    
    <!-- 按作者分组的普通文章 -->
    {{ range $authorKey, $author := .Site.Data.authors }}
      {{ if ne $authorKey "featured" }}
        {{ $authorPosts := where $allPosts ".Params.author" "eq" $authorKey }}
        {{ $nonFeaturedPosts := where $authorPosts ".Params.featured" "ne" true }}
        {{ range $nonFeaturedPosts.ByWeight }}
          <li class="article-list-item reveal index-post-list" 
              data-author="{{ .Params.author | default "spixed" }}"
              data-featured="false"
              data-weight="{{ .Params.weight | default 999 }}"
              data-date="{{ .Date.Unix }}">
            
            {{ if (.Params.thumbnail) }}
              <div class="article-list-img-else">
                <div class="article-list-img" style="background-image: url(&quot;{{ .Params.thumbnail }}&quot;);"></div>
                <div class="article-list-img-right">
                  {{ partial "components/home/<USER>" . }}
                </div>
              </div>
            {{ else }}
              {{ partial "components/home/<USER>" . }}
            {{ end }}
          </li>
        {{ end }}
      {{ end }}
    {{ end }}
    
    <!-- 没有指定作者的文章（默认为spixed） -->
    {{ $noAuthorPosts := where $allPosts ".Params.author" "eq" nil }}
    {{ $nonFeaturedNoAuthor := where $noAuthorPosts ".Params.featured" "ne" true }}
    {{ range $nonFeaturedNoAuthor.ByWeight }}
      <li class="article-list-item reveal index-post-list" 
          data-author="spixed"
          data-featured="false"
          data-weight="{{ .Params.weight | default 999 }}"
          data-date="{{ .Date.Unix }}">
        
        {{ if (.Params.thumbnail) }}
          <div class="article-list-img-else">
            <div class="article-list-img" style="background-image: url(&quot;{{ .Params.thumbnail }}&quot;);"></div>
            <div class="article-list-img-right">
              {{ partial "components/home/<USER>" . }}
            </div>
          </div>
        {{ else }}
          {{ partial "components/home/<USER>" . }}
        {{ end }}
      </li>
    {{ end }}
  </ul>
  
  <!-- 无限滚动加载指示器 -->
  <div class="infinite-scroll-indicator" id="infiniteScrollIndicator" style="display: none;">
    <div class="loading-spinner">
      <i class="ri-loader-4-line"></i>
    </div>
    <span>加载中...</span>
  </div>
  
  <!-- 无文章提示 -->
  <div class="no-articles" id="noArticles" style="display: none;">
    <div class="no-articles-icon">
      <i class="ri-file-text-line"></i>
    </div>
    <p>该作者暂无文章</p>
  </div>
</div>

<!-- 将文章数据传递给JavaScript -->
<script>
  window.articlesData = [
    {{ $posts := slice }}
    {{ range $allPosts }}
      {{ $posts = $posts | append . }}
    {{ end }}
    {{ range $index, $post := $posts }}
      {
        author: "{{ $post.Params.author | default "spixed" }}",
        featured: {{ $post.Params.featured | default false }},
        weight: {{ $post.Params.weight | default 999 }},
        date: {{ $post.Date.Unix }},
        title: {{ $post.Title | jsonify }},
        summary: {{ $post.Summary | jsonify }},
        permalink: "{{ $post.Permalink | relURL }}",
        categories: [{{ range $post.Params.Categories }}"{{ . }}"{{ if ne . (index (last 1 $post.Params.Categories) 0) }},{{ end }}{{ end }}],
        tags: [{{ range $post.Params.Tags }}"{{ . }}"{{ if ne . (index (last 1 $post.Params.Tags) 0) }},{{ end }}{{ end }}],
        dateFormatted: "{{ $post.Date.Format "06-01-02" }}",
        thumbnail: "{{ $post.Params.thumbnail }}",
        status: {{ $post.Params.status | default false }},
        statusCate: "{{ $post.Params.statusCate }}",
        buy: {{ $post.Params.buy | default false }}
      }{{ if lt $index (sub (len $posts) 1) }},{{ end }}
    {{ end }}
  ];
</script>
