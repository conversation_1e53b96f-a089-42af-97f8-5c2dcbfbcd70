<!-- 作者选择器组件 -->
<div class="author-selector-area" id="authorSelectorArea">
  <!-- 正(n+1)边形作者选择器 -->
  <div class="author-polygon" id="authorPolygon">
    <!-- 动态渲染所有作者头像 -->
    {{ range $key, $author := .Site.Data.authors }}
      <img src="{{ $author.avatar | relURL }}" 
           class="author-avatar" 
           data-author="{{ $key }}"
           data-name="{{ $author.name }}"
           data-nickname="{{ $author.nickname }}"
           data-bio="{{ $author.bio }}"
           alt="{{ $author.name }}">
    {{ end }}
  </div>
  
  <!-- 中心作者信息卡片 -->
  <div class="author-center-info" id="centerInfo">
    <div class="center-avatar-container">
      <img src="/site/logo.png"
           class="center-avatar-img"
           id="centerAvatarImg"
           alt="精选文章">
    </div>
    <div class="center-author-details" id="centerAuthorDetails">
      <h3 class="author-nickname" id="authorNickname">精选文章</h3>
      <p class="author-bio" id="authorBio">精心挑选的优质内容</p>
      <div class="author-social" id="authorSocial">
        <!-- 精选文章模式下不显示社交链接，JavaScript会动态更新 -->
      </div>
    </div>
  </div>
</div>

<!-- 移动端顶部作者头像触发器 -->
<div class="mobile-author-trigger" id="mobileAuthorTrigger">
  <img src="/site/logo.png"
       class="mobile-trigger-avatar"
       id="mobileTriggerAvatar"
       alt="点击选择作者">
  <span class="mobile-trigger-name" id="mobileTriggerName">精选文章</span>
</div>

<!-- 移动端作者选择弹窗 -->
<div class="mobile-author-modal" id="mobileAuthorModal">
  <div class="modal-overlay" id="modalOverlay"></div>
  <div class="modal-content">
    <div class="modal-header">
      <h3>选择作者</h3>
      <button class="modal-close" id="modalClose" type="button">
        <i class="ri-close-line"></i>
      </button>
    </div>
    <div class="modal-body">
      <!-- 移动端也使用正(n+1)边形布局 -->
      <div class="mobile-author-polygon" id="mobileAuthorPolygon">
        {{ range $key, $author := .Site.Data.authors }}
          <div class="mobile-author-item" data-author="{{ $key }}">
            <img src="{{ $author.avatar | relURL }}" alt="{{ $author.name }}">
            <span class="mobile-author-name">{{ $author.nickname }}</span>
          </div>
        {{ end }}
      </div>
    </div>
  </div>
</div>



<!-- 将作者数据传递给JavaScript -->
<script>
  window.authorsData = {
    {{ $authorKeys := slice }}
    {{ range $key, $author := .Site.Data.authors }}
      {{ $authorKeys = $authorKeys | append $key }}
    {{ end }}
    {{ range $index, $key := $authorKeys }}
      {{ $author := index $.Site.Data.authors $key }}
      "{{ $key }}": {
        name: "{{ $author.name }}",
        nickname: "{{ $author.nickname }}",
        avatar: "{{ $author.avatar | relURL }}",
        bio: "{{ $author.bio }}",
        github: "{{ $author.github }}",
        email: "{{ $author.email }}",
        website: "{{ $author.website }}",
        weight: {{ $author.weight | default 999 }}
      }{{ if lt $index (sub (len $authorKeys) 1) }},{{ end }}
    {{ end }}
  };

  // 设置默认选中的作者
  window.currentAuthor = "spixed";
</script>
