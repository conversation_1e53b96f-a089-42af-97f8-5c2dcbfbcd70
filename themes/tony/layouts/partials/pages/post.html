<div id="post-container">
    <div class="grid grid-centered">
        <div id="grid-cell" class="grid-cell">
            {{ if .Params.adjacentPost | default .Site.Params.enableAdjacentPost }}
                <div class="single-left">
                    {{ partial "components/post/toc.html" . }}
                    {{ partial "components/post/adjacent-posts.html" . }}
                </div>
            {{ end }}
            {{ if .Params.readingBar | default .Site.Params.enableReadingBar }}
                <div id="reading-bar"></div>
            {{ end }}
            <article class="article reveal">
                <div id="load">
                    <div class="article-header">
                        {{ partial "components/post/meta.html" . }}
                    </div>
                    <div class="article-content">
                        {{ .Content }}
                    </div>
                    {{ if .Params.buy }}
                        <div>
                            <div class="buy-list-item" style="margin: 60px 10px -35px 10px;">
                                <div class="buy-left-img-noborder">
                                    <img src="{{ .Params.buyImage }}" alt="{{ .Params.buyName }}">
                                </div>
                                <div class="buy-right-info">
                                    <div>
                                        <h3>{{ .Params.buyName }}</h3>
                                        <p>{{ .Params.buyInfo }}</p>
                                    </div>
                                    <div>
                                        <a href="{{ .Params.buyLink }}">{{ .Params.buyButtonText }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {{ end }}

                    {{ if .Params.Tags }}
                        {{ partial "components/post/tags.html" . }}
                    {{ end }}
                    {{ partial "components/comments.html" . }}
                </div>
            </div>
        </div>
    </div>
</div>