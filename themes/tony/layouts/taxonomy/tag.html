<!DOCTYPE html>
<html lang="zh">
    {{ partial "head.html" . }}
    <body>
        {{ partial "header.html" . }}
        <div id="index">
            <div class="grid grid-centered" style="max-width: 660px; padding: 0px 20px; margin-top: 80px;">
                <div id="grid-cell" class="grid-cell">
                    {{ $paginator := .Paginate (where .Site.RegularPages "Section" "in" .Site.Params.mainSections) }}

                    {{ partial "pages/taxonomy-header.html" . }}
                    <ul class="article-list">
                    {{ range .Data.Pages }}
                        <li class="article-list-item reveal index-post-list">
                            <a href="{{.RelPermalink}}" style="text-decoration: none;">
                                <h5 style="margin: 15px 0px 0px; padding: 0px;">
                                    {{ .Title }}
                                </h5>
                            </a>
                            <p>
                                {{ .Summary }}...
                            </p>
                            <div class="article-list-footer">
                                <span class="article-list-date">
                                    {{ .Date.Format "06-01-02" }}
                                </span>
                            </div>
                        </li>
                    {{ end }}
                    </ul>
                </div>
            </div>
        </div>
        {{ partial "footer.html" . }}
        {{ partial "script.html" . }}
    </body>
</html>
