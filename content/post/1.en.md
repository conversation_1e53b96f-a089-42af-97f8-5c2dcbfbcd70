+++
title = 'My First Blog'
date = 2024-02-13T16:34:38+08:00
draft = false
weight = 0
+++

> Brief Summary: Self introduction, my past as a blogger, purposes and reasons for creating this blog, my self-requirements about writing blogs, and a test about coding blocks.

<!--more-->

# Introduction

Hello, everyone! My name's Spixed, and this is my first blog.

I tried to create a blog and succeeded before, but at that time I make it just for fun. Thus I didn't write any posts and I have no individual.

But now it's different. I want to make a blog to record my coding life and my senior high life. I'll write passages and post on this blog from time to time. I plan to make a detailed introduction about my teachers COZ they have plenty of *fun habits* (/doge). **This introduction may be completed when I finish senior high.**

Anyway, I'm a Chinese student, so I try to use both Chinese and English to write my blogs. But if I just use Chinese or English, please forgive me. **And if there're some grammar errors please write a comment to tell me.**

> Well, I don't want to write *Chinese* for this passage because I need to study as soon as possible ... (っ °Д °;)っ

That's all. Thank you for reading.

# Examples(Testing)

> After a lot of hard work, in dense documentation, in messy directory structure, and without any comments, I managed to fix this bug. 🥳

```cpp
#include <iostream>
using namespace std;

int main() {
    string name;
    int year, now;

    printf("Type your name here: ")
    getline(cin, name);
    printf("Hello, %s! \nWhat year were you born? ", name);
    getline(cin, year);
    printf("Then what year is it?");
    getline(cin, now)
    printf("So congratulations on being a workhorse for the world for %d years! ", now - year);

    return 0;
}
```

> And honestly I barely write English version fully by myself -- usually I write Chinese version and then I let DeepSeek to translate it into English. And this quote is written by myself. 