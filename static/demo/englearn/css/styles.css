@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=Noto+Serif+SC:wght@400;600&display=swap');

body {
    font-family: 'Noto Sans SC', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.highlight {
    position: relative;
    z-index: 1;
}

.highlight:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40%;
    background-color: rgba(255, 228, 132, 0.5);
    z-index: -1;
    transform: rotate(-1deg);
}

/* 颜色主题 */
.bg-amber-500 { background-color: #f59e0b; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-green-500 { background-color: #10b981; }
.bg-purple-500 { background-color: #8b5cf6; }
.bg-indigo-500 { background-color: #6366f1; }
.bg-red-500 { background-color: #ef4444; }

.text-amber-600 { color: #d97706; }
.text-blue-600 { color: #2563eb; }
.text-green-600 { color: #059669; }
.text-purple-600 { color: #7c3aed; }
.text-indigo-600 { color: #4f46e5; }
.text-red-600 { color: #dc2626; }

.bg-amber-50 { background-color: #fffbeb; }
.bg-indigo-50 { background-color: #eef2ff; }
.bg-red-50 { background-color: #fef2f2; }

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.table-container table {
    width: 100%;
    border-collapse: collapse;
}

.table-container th {
    padding-bottom: 0.75rem;
    font-weight: bold;
    color: #374151;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.table-container td {
    padding: 1rem 0;
    color: #6b7280;
    border-bottom: 1px solid #e5e7eb;
}

.table-container tr:last-child td {
    border-bottom: none;
}

.table-container .word-cell {
    font-weight: bold;
    color: #1f2937;
}

.table-container .meaning-cell {
    color: #374151;
}

/* 示例文本样式 */
.example-text {
    font-family: 'Noto Serif SC', serif;
    font-style: italic;
    color: #4b5563;
    line-height: 1.7;
    font-size: 0.95rem;
}

/* 网格样式 */
.grid-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr 1fr;
    }
}

@media (min-width: 1200px) {
    .grid-container {
        grid-template-columns: 1fr;
    }
    
    .wide-layout .grid-container {
        grid-template-columns: 1fr;
    }
}

.grid-item {
    background-color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
}

.grid-item h3 {
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.grid-item p {
    color: #374151;
    margin-bottom: 0.5rem;
}

.grid-item .example-text {
    color: #6b7280;
    margin-top: 0.5rem;
}

/* 警告框样式 */
.warning-item {
    margin-bottom: 1.5rem;
}

.warning-item h3 {
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.warning-item .content {
    color: #374151;
    margin-bottom: 0.5rem;
}

.warning-box {
    background-color: #fef2f2;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-top: 0.5rem;
}

.warning-box .correct {
    color: #059669;
}

.warning-box .incorrect {
    color: #dc2626;
}

/* 示例样式 */
.example-item {
    background-color: #eef2ff;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.example-item h3 {
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.example-item .content {
    color: #374151;
}

/* 总结样式 */
.summary-container {
    background-color: #fffbeb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2.5rem;
}

.summary-header {
    display: flex;
    align-items: flex-start;
}

.summary-icon {
    flex-shrink: 0;
    background-color: #f59e0b;
    border-radius: 50%;
    padding: 0.75rem;
    margin-right: 1rem;
}

.summary-icon i {
    color: white;
    font-size: 1.25rem;
}

.summary-content h2 {
    font-size: 1.25rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.summary-content p {
    color: #374151;
    margin-bottom: 0.75rem;
}

.tips-box {
    background-color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #f59e0b;
}

.tips-box h3 {
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.tips-box ul {
    list-style-type: disc;
    padding-left: 1.25rem;
}

.tips-box li {
    color: #374151;
    margin-bottom: 0.25rem;
}

/* 高亮词汇样式 */
.example-text span {
    font-weight: 600;
    text-decoration: none;
    border-radius: 2px;
    padding: 0 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .max-w-4xl {
        max-width: 100%;
        padding: 0 1rem;
    }
    
    .text-3xl {
        font-size: 1.875rem;
    }
    
    .text-4xl {
        font-size: 2.25rem;
    }
    
    .grid-container {
        grid-template-columns: 1fr;
    }
    
    .summary-header {
        flex-direction: column;
        text-align: center;
    }
    
    .summary-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .example-text {
        font-size: 0.9rem;
    }
}

/* 宽屏布局优化 */
@media (min-width: 1200px) {
    .wide-layout .card {
        height: fit-content;
    }
    
    .wide-layout .column-2 .card,
    .wide-layout .column-3 .card {
        margin-bottom: 2rem;
    }
    
    .wide-layout .width-full {
        margin-bottom: 2rem;
    }
}

/* 智能网格布局 */
.smart-grid-layout {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* 网格行 */
.grid-row {
    display: flex;
    gap: 2rem;
}

/* 顶部行 - 占据全宽 */
.top-row {
    width: 100%;
}

.top-row .grid-item.span-1 {
    flex: 1;
}

.top-row .grid-item.span-2 {
    flex: 2;
}

.top-row .grid-item.span-3 {
    flex: 3;
}

/* 中间行 - 左右并排 */
.middle-row {
    width: 100%;
    justify-content: space-between;
}

.middle-row .grid-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.middle-row .left-column {
    flex: 1;
    margin-right: 1rem;
}

.middle-row .right-column {
    flex: 1;
    margin-left: 1rem;
}

/* 底部行 - 左右并排 */
.bottom-row {
    width: 100%;
    justify-content: space-between;
}

.bottom-row .grid-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.bottom-row .bottom-left-column {
    flex: 1;
    margin-right: 1rem;
}

.bottom-row .bottom-right-column {
    flex: 1;
    margin-left: 1rem;
}

/* 网格项 */
.grid-item {
    width: 100%;
}

.grid-item .card {
    height: 100%;
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 1199px) {
    .smart-grid-layout {
        gap: 1.5rem;
    }
    
    .grid-row {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .middle-row .left-column,
    .middle-row .right-column,
    .bottom-row .bottom-left-column,
    .bottom-row .bottom-right-column {
        margin: 0;
    }
    
    .top-row .grid-item.span-1,
    .top-row .grid-item.span-2,
    .top-row .grid-item.span-3 {
        flex: 1;
    }
}

@media (min-width: 1200px) {
    .smart-grid-layout {
        gap: 2.5rem;
    }
    
    .grid-row {
        gap: 2.5rem;
    }
    
    .middle-row .grid-column,
    .bottom-row .grid-column {
        gap: 2rem;
    }
    
    /* 优化不同区域的间距 */
    .top-row .card {
        margin-bottom: 0;
    }
    
    .middle-row .card {
        margin-bottom: 0;
    }
    
    .bottom-row .card {
        margin-bottom: 0;
    }
    
    /* 底部区域更紧凑 */
    .bottom-row .grid-column {
        gap: 1.5rem;
    }
    
    .bottom-row .card .p-6 {
        padding: 1.25rem;
    }
    
    .bottom-row .grid-container {
        gap: 1rem;
    }
    
    .bottom-row .grid-item {
        padding: 0.75rem;
    }
} 